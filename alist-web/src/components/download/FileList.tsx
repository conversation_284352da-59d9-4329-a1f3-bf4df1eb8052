import { createSignal, Show, For, createMemo } from "solid-js"
import {
  Box,
  HStack,
  VStack,
  Text,
  Button,
  Badge,
  Collapse,
  Input,
  InputGroup,
  InputLeftElement,
  Icon,
  Tooltip,
  Flex,
  Spacer
} from "@hope-ui/solid"
import { useT } from "~/hooks"
import { FailedFile } from "~/utils/download-queue"

// 文件列表属性接口
export interface FileListProps {
  files: FailedFile[]
  title: string
  type: 'failed' | 'completed' | 'processing'
  maxHeight?: string
  showRetry?: boolean
  onRetry?: (file: FailedFile) => void
  onRetryAll?: () => void
  onClear?: () => void
}

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  switch (ext) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      return '🖼️'
    case 'mp4':
    case 'avi':
    case 'mkv':
    case 'mov':
      return '🎥'
    case 'mp3':
    case 'wav':
    case 'flac':
      return '🎵'
    case 'pdf':
      return '📄'
    case 'doc':
    case 'docx':
      return '📝'
    case 'xls':
    case 'xlsx':
      return '📊'
    case 'zip':
    case 'rar':
    case '7z':
      return '📦'
    case 'txt':
      return '📃'
    default:
      return '📁'
  }
}

// 获取错误类型颜色
const getErrorTypeColor = (error: string) => {
  if (error.includes('network') || error.includes('fetch')) return 'danger'
  if (error.includes('timeout')) return 'warning'
  if (error.includes('permission') || error.includes('403')) return 'danger'
  if (error.includes('not found') || error.includes('404')) return 'neutral'
  return 'danger'
}

// 单个文件项组件
const FileItem = (props: {
  file: FailedFile
  type: 'failed' | 'completed' | 'processing'
  showRetry?: boolean
  onRetry?: (file: FailedFile) => void
}) => {
  const t = useT()
  const [expanded, setExpanded] = createSignal(false)

  return (
    <Box
      w="$full"
      p="$3"
      bg={props.type === 'failed' ? '$danger3' : props.type === 'completed' ? '$success3' : '$info3'}
      rounded="$md"
      border="1px solid"
      borderColor={props.type === 'failed' ? '$danger6' : props.type === 'completed' ? '$success6' : '$info6'}
    >
      <HStack w="$full" justifyContent="space-between" alignItems="start">
        {/* 文件信息 */}
        <HStack spacing="$2" flex="1" alignItems="start">
          <Text fontSize="lg">{getFileIcon(props.file.file.path)}</Text>
          <VStack alignItems="start" spacing="$1" flex="1">
            <Text 
              fontSize="sm" 
              fontWeight="medium"
              css={{ wordBreak: "break-all" }}
              cursor="pointer"
              onClick={() => setExpanded(!expanded())}
            >
              {props.file.file.path.split('/').pop() || props.file.file.path}
            </Text>
            
            <HStack spacing="$2" flexWrap="wrap">
              <Show when={props.file.file.size}>
                <Badge size="sm" variant="outline">
                  {formatBytes(props.file.file.size!)}
                </Badge>
              </Show>
              
              <Show when={props.type === 'failed'}>
                <Badge 
                  size="sm" 
                  colorScheme={getErrorTypeColor(props.file.error)}
                  variant="solid"
                >
                  重试 {props.file.retryCount}/3
                </Badge>
              </Show>
              
              <Show when={props.type === 'completed'}>
                <Badge size="sm" colorScheme="success" variant="solid">
                  ✓ 完成
                </Badge>
              </Show>
              
              <Show when={props.type === 'processing'}>
                <Badge size="sm" colorScheme="info" variant="solid">
                  ⏳ 处理中
                </Badge>
              </Show>
            </HStack>

            {/* 展开的详细信息 */}
            <Collapse in={expanded()}>
              <VStack alignItems="start" spacing="$1" mt="$2" p="$2" bg="$blackAlpha4" rounded="$sm">
                <Text fontSize="xs" color="$neutral11">
                  完整路径: {props.file.file.path}
                </Text>
                <Show when={props.type === 'failed'}>
                  <Text fontSize="xs" color="$danger11">
                    错误信息: {props.file.error}
                  </Text>
                </Show>
                <Show when={props.file.file.url}>
                  <Text 
                    fontSize="xs" 
                    color="$info11" 
                    css={{ wordBreak: "break-all" }}
                  >
                    下载链接: {props.file.file.url.substring(0, 100)}...
                  </Text>
                </Show>
              </VStack>
            </Collapse>
          </VStack>
        </HStack>

        {/* 操作按钮 */}
        <VStack spacing="$1">
          <Show when={props.showRetry && props.file.retryCount < 3}>
            <Tooltip label="重试此文件">
              <Button 
                size="xs" 
                colorScheme="warning" 
                variant="ghost"
                onClick={() => props.onRetry?.(props.file)}
              >
                🔄
              </Button>
            </Tooltip>
          </Show>
          
          <Tooltip label={expanded() ? "收起详情" : "展开详情"}>
            <Button 
              size="xs" 
              variant="ghost"
              onClick={() => setExpanded(!expanded())}
            >
              {expanded() ? '▲' : '▼'}
            </Button>
          </Tooltip>
        </VStack>
      </HStack>
    </Box>
  )
}

export const FileList = (props: FileListProps) => {
  const t = useT()
  const [searchTerm, setSearchTerm] = createSignal('')
  const [isExpanded, setIsExpanded] = createSignal(false)

  // 过滤文件
  const filteredFiles = createMemo(() => {
    const term = searchTerm().toLowerCase()
    if (!term) return props.files
    
    return props.files.filter(file => 
      file.file.path.toLowerCase().includes(term) ||
      file.error.toLowerCase().includes(term)
    )
  })

  // 统计信息
  const stats = createMemo(() => {
    const total = props.files.length
    const retryable = props.files.filter(f => f.retryCount < 3).length
    const totalSize = props.files.reduce((sum, f) => sum + (f.file.size || 0), 0)
    
    return { total, retryable, totalSize }
  })

  return (
    <Box w="$full">
      {/* 头部 */}
      <HStack justifyContent="space-between" mb="$3">
        <HStack spacing="$2">
          <Text fontSize="sm" fontWeight="bold">
            {props.title}
          </Text>
          <Badge 
            colorScheme={props.type === 'failed' ? 'danger' : props.type === 'completed' ? 'success' : 'info'}
            variant="solid"
          >
            {stats().total}
          </Badge>
          <Show when={stats().totalSize > 0}>
            <Badge variant="outline" size="sm">
              {formatBytes(stats().totalSize)}
            </Badge>
          </Show>
        </HStack>

        <HStack spacing="$1">
          <Show when={props.showRetry && stats().retryable > 0}>
            <Tooltip label="重试所有可重试的文件">
              <Button 
                size="xs" 
                colorScheme="warning"
                onClick={props.onRetryAll}
              >
                🔄 重试全部 ({stats().retryable})
              </Button>
            </Tooltip>
          </Show>
          
          <Show when={props.onClear}>
            <Tooltip label="清空列表">
              <Button 
                size="xs" 
                colorScheme="neutral" 
                variant="ghost"
                onClick={props.onClear}
              >
                🗑️
              </Button>
            </Tooltip>
          </Show>
          
          <Button 
            size="xs" 
            variant="ghost"
            onClick={() => setIsExpanded(!isExpanded())}
          >
            {isExpanded() ? '收起' : '展开'}
          </Button>
        </HStack>
      </HStack>

      {/* 搜索框 */}
      <Show when={isExpanded() && props.files.length > 5}>
        <InputGroup mb="$3">
          <InputLeftElement>
            <Text>🔍</Text>
          </InputLeftElement>
          <Input
            placeholder="搜索文件名或错误信息..."
            value={searchTerm()}
            onInput={(e) => setSearchTerm(e.currentTarget.value)}
            size="sm"
          />
        </InputGroup>
      </Show>

      {/* 文件列表 */}
      <Collapse in={isExpanded()}>
        <VStack 
          spacing="$2" 
          maxH={props.maxHeight || "$64"} 
          overflowY="auto"
          css={{
            '&::-webkit-scrollbar': {
              width: '6px'
            },
            '&::-webkit-scrollbar-track': {
              background: 'var(--hope-colors-neutral3)'
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'var(--hope-colors-neutral6)',
              borderRadius: '3px'
            }
          }}
        >
          <Show 
            when={filteredFiles().length > 0}
            fallback={
              <Box p="$4" textAlign="center">
                <Text fontSize="sm" color="$neutral11">
                  {searchTerm() ? '没有找到匹配的文件' : '暂无文件'}
                </Text>
              </Box>
            }
          >
            <For each={filteredFiles()}>
              {(file) => (
                <FileItem
                  file={file}
                  type={props.type}
                  showRetry={props.showRetry}
                  onRetry={props.onRetry}
                />
              )}
            </For>
          </Show>
        </VStack>
      </Collapse>

      {/* 快速预览（收起状态） */}
      <Show when={!isExpanded() && props.files.length > 0}>
        <Box p="$2" bg="$neutral2" rounded="$md">
          <Text fontSize="xs" color="$neutral11">
            {props.files.slice(0, 3).map(f => f.file.path.split('/').pop()).join(', ')}
            {props.files.length > 3 && ` 等 ${props.files.length} 个文件`}
          </Text>
        </Box>
      </Show>
    </Box>
  )
}
