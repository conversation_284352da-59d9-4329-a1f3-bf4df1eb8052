import { createSignal, createEffect, Show, For } from "solid-js"
import {
  Box,
  HStack,
  VStack,
  Text,
  Progress,
  Badge,
  Button,
  Tooltip,
  Icon,
  Flex,
  Spacer,
  Circle
} from "@hope-ui/solid"
import { useT } from "~/hooks"

// 进度指示器的属性接口
export interface ProgressIndicatorProps {
  phase: 'preparing' | 'ready' | 'scanning' | 'downloading' | 'compressing' | 'complete' | 'error' | 'cancelled'
  current: number
  total: number
  percentage: number
  speed?: number
  currentItem?: string
  estimatedTime?: number
  showDetails?: boolean
  onToggleDetails?: () => void
}

// 格式化速度
const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond === 0) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || seconds === Infinity) return '--'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

// 获取阶段图标
const getPhaseIcon = (phase: string) => {
  switch (phase) {
    case 'scanning': return '🔍'
    case 'downloading': return '⬇️'
    case 'compressing': return '📦'
    case 'complete': return '✅'
    case 'error': return '❌'
    case 'cancelled': return '⏹️'
    default: return '⏳'
  }
}

// 获取阶段颜色
const getPhaseColor = (phase: string) => {
  switch (phase) {
    case 'scanning': return 'info'
    case 'downloading': return 'primary'
    case 'compressing': return 'warning'
    case 'complete': return 'success'
    case 'error': return 'danger'
    case 'cancelled': return 'neutral'
    default: return 'neutral'
  }
}

export const ProgressIndicator = (props: ProgressIndicatorProps) => {
  const t = useT()
  const [animatedPercentage, setAnimatedPercentage] = createSignal(0)

  // 动画效果
  createEffect(() => {
    const target = props.percentage
    const current = animatedPercentage()
    
    if (Math.abs(target - current) > 0.1) {
      const step = (target - current) * 0.1
      const timer = setTimeout(() => {
        setAnimatedPercentage(current + step)
      }, 16) // 60fps
      
      return () => clearTimeout(timer)
    } else {
      setAnimatedPercentage(target)
    }
  })

  return (
    <VStack spacing="$3" w="$full">
      {/* 阶段状态头部 */}
      <HStack w="$full" justifyContent="space-between" alignItems="center">
        <HStack spacing="$2">
          <Text fontSize="lg">{getPhaseIcon(props.phase)}</Text>
          <Badge 
            colorScheme={getPhaseColor(props.phase)}
            variant="solid"
            size="lg"
          >
            {t(`home.package_download.phase.${props.phase}`)}
          </Badge>
          <Show when={props.speed && props.speed > 0}>
            <Badge colorScheme="success" variant="outline">
              {formatSpeed(props.speed)}
            </Badge>
          </Show>
        </HStack>
        
        <Show when={props.onToggleDetails}>
          <Button 
            size="xs" 
            variant="ghost"
            onClick={props.onToggleDetails}
          >
            {props.showDetails ? '隐藏详情' : '显示详情'}
          </Button>
        </Show>
      </HStack>

      {/* 主进度条 */}
      <Box w="$full">
        <HStack justifyContent="space-between" mb="$2">
          <Text fontSize="sm" fontWeight="medium">
            {t("home.package_download.progress")}: {props.current}/{props.total}
          </Text>
          <Text fontSize="sm" fontWeight="bold" color={getPhaseColor(props.phase)}>
            {animatedPercentage().toFixed(1)}%
          </Text>
        </HStack>
        
        <Progress 
          value={animatedPercentage()} 
          size="lg"
          colorScheme={getPhaseColor(props.phase)}
          css={{
            '& > div': {
              transition: 'width 0.3s ease-in-out'
            }
          }}
        />
      </Box>

      {/* 详细信息 */}
      <Show when={props.showDetails}>
        <VStack spacing="$2" w="$full" p="$3" bg="$neutral2" rounded="$md">
          {/* 当前处理项 */}
          <Show when={props.currentItem}>
            <HStack w="$full" spacing="$2">
              <Text fontSize="xs" color="$neutral11" minW="fit-content">
                当前项目:
              </Text>
              <Text 
                fontSize="xs" 
                css={{ 
                  wordBreak: "break-all",
                  flex: 1
                }}
                color="$neutral12"
              >
                {props.currentItem}
              </Text>
            </HStack>
          </Show>

          {/* 预计剩余时间 */}
          <Show when={props.estimatedTime}>
            <HStack w="$full" justifyContent="space-between">
              <Text fontSize="xs" color="$neutral11">
                预计剩余时间:
              </Text>
              <Text fontSize="xs" color="$neutral12">
                {formatTime(props.estimatedTime!)}
              </Text>
            </HStack>
          </Show>

          {/* 处理速度 */}
          <Show when={props.speed}>
            <HStack w="$full" justifyContent="space-between">
              <Text fontSize="xs" color="$neutral11">
                处理速度:
              </Text>
              <Text fontSize="xs" color="$success11">
                {formatSpeed(props.speed!)}
              </Text>
            </HStack>
          </Show>
        </VStack>
      </Show>
    </VStack>
  )
}

// 迷你进度指示器（用于紧凑显示）
export const MiniProgressIndicator = (props: Pick<ProgressIndicatorProps, 'phase' | 'percentage' | 'current' | 'total'>) => {
  return (
    <HStack spacing="$2" alignItems="center">
      <Text fontSize="sm">{getPhaseIcon(props.phase)}</Text>
      <Progress 
        value={props.percentage} 
        size="sm"
        colorScheme={getPhaseColor(props.phase)}
        w="$20"
      />
      <Text fontSize="xs" color="$neutral11">
        {props.current}/{props.total}
      </Text>
      <Text fontSize="xs" fontWeight="bold" color={getPhaseColor(props.phase)}>
        {props.percentage.toFixed(0)}%
      </Text>
    </HStack>
  )
}

// 环形进度指示器
export const CircularProgressIndicator = (props: Pick<ProgressIndicatorProps, 'phase' | 'percentage'>) => {
  const circumference = 2 * Math.PI * 45 // 半径45的圆周长
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (props.percentage / 100) * circumference

  return (
    <Box position="relative" w="$24" h="$24">
      <svg width="96" height="96" viewBox="0 0 96 96">
        {/* 背景圆 */}
        <circle
          cx="48"
          cy="48"
          r="45"
          fill="none"
          stroke="currentColor"
          stroke-width="6"
          opacity="0.1"
        />
        {/* 进度圆 */}
        <circle
          cx="48"
          cy="48"
          r="45"
          fill="none"
          stroke="currentColor"
          stroke-width="6"
          stroke-linecap="round"
          stroke-dasharray={strokeDasharray}
          stroke-dashoffset={strokeDashoffset}
          transform="rotate(-90 48 48)"
          css={{
            transition: 'stroke-dashoffset 0.3s ease-in-out',
            color: `var(--hope-colors-${getPhaseColor(props.phase)}9)`
          }}
        />
      </svg>
      
      {/* 中心内容 */}
      <Flex
        position="absolute"
        top="0"
        left="0"
        w="$full"
        h="$full"
        alignItems="center"
        justifyContent="center"
        direction="column"
      >
        <Text fontSize="lg">{getPhaseIcon(props.phase)}</Text>
        <Text fontSize="xs" fontWeight="bold" color={getPhaseColor(props.phase)}>
          {props.percentage.toFixed(0)}%
        </Text>
      </Flex>
    </Box>
  )
}
