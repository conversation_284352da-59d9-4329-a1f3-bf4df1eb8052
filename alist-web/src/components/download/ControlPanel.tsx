import { createSignal, Show, createMemo } from "solid-js"
import {
  Box,
  HStack,
  VStack,
  Text,
  Button,
  ButtonGroup,
  Badge,
  Tooltip,
  Input,
  Switch,
  FormControl,
  FormLabel,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from "@hope-ui/solid"
import { useT } from "~/hooks"

// 控制面板属性接口
export interface ControlPanelProps {
  phase: 'preparing' | 'ready' | 'scanning' | 'downloading' | 'compressing' | 'complete' | 'error' | 'cancelled'
  isPaused?: boolean
  canPause?: boolean
  canCancel?: boolean
  canRetry?: boolean
  canResume?: boolean
  
  // 设置
  concurrency?: number
  maxConcurrency?: number
  enableAutoRetry?: boolean
  enableMemoryOptimization?: boolean
  
  // 统计信息
  totalFiles?: number
  completedFiles?: number
  failedFiles?: number
  speed?: number
  estimatedTime?: number
  
  // 回调函数
  onPause?: () => void
  onResume?: () => void
  onCancel?: () => void
  onRetry?: () => void
  onClose?: () => void
  onConcurrencyChange?: (value: number) => void
  onAutoRetryChange?: (enabled: boolean) => void
  onMemoryOptimizationChange?: (enabled: boolean) => void
}

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || seconds === Infinity) return '--'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

// 格式化速度
const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond === 0) return '0 B/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取阶段状态
const getPhaseStatus = (phase: string) => {
  switch (phase) {
    case 'scanning': return { color: 'info', text: '扫描中', icon: '🔍' }
    case 'downloading': return { color: 'primary', text: '下载中', icon: '⬇️' }
    case 'compressing': return { color: 'warning', text: '压缩中', icon: '📦' }
    case 'complete': return { color: 'success', text: '已完成', icon: '✅' }
    case 'error': return { color: 'danger', text: '出错', icon: '❌' }
    case 'cancelled': return { color: 'neutral', text: '已取消', icon: '⏹️' }
    default: return { color: 'neutral', text: '未知', icon: '❓' }
  }
}

export const ControlPanel = (props: ControlPanelProps) => {
  const t = useT()
  const [showAdvanced, setShowAdvanced] = createSignal(false)
  
  const phaseStatus = createMemo(() => getPhaseStatus(props.phase))
  
  // 计算完成率
  const completionRate = createMemo(() => {
    if (!props.totalFiles || props.totalFiles === 0) return 0
    return Math.round((props.completedFiles || 0) / props.totalFiles * 100)
  })

  // 是否显示警告
  const showWarning = createMemo(() => {
    return props.failedFiles && props.failedFiles > 0
  })

  return (
    <VStack spacing="$4" w="$full">
      {/* 状态概览 */}
      <Box w="$full" p="$4" bg="$neutral2" rounded="$lg">
        <HStack justifyContent="space-between" alignItems="center" mb="$3">
          <HStack spacing="$3">
            <Text fontSize="xl">{phaseStatus().icon}</Text>
            <VStack alignItems="start" spacing="$1">
              <Badge 
                colorScheme={phaseStatus().color}
                variant="solid"
                size="lg"
              >
                {phaseStatus().text}
              </Badge>
              <Show when={props.isPaused}>
                <Badge colorScheme="warning" variant="outline" size="sm">
                  ⏸️ 已暂停
                </Badge>
              </Show>
            </VStack>
          </HStack>
          
          <VStack alignItems="end" spacing="$1">
            <Text fontSize="lg" fontWeight="bold" color={phaseStatus().color}>
              {completionRate()}%
            </Text>
            <Show when={props.speed && props.speed > 0}>
              <Text fontSize="sm" color="$success11">
                {formatSpeed(props.speed)}
              </Text>
            </Show>
          </VStack>
        </HStack>

        {/* 统计信息 */}
        <HStack justifyContent="space-between" fontSize="sm">
          <VStack alignItems="start" spacing="$1">
            <Text color="$neutral11">总文件数</Text>
            <Text fontWeight="bold">{props.totalFiles || 0}</Text>
          </VStack>
          <VStack alignItems="center" spacing="$1">
            <Text color="$neutral11">已完成</Text>
            <Text fontWeight="bold" color="$success11">{props.completedFiles || 0}</Text>
          </VStack>
          <VStack alignItems="center" spacing="$1">
            <Text color="$neutral11">失败</Text>
            <Text fontWeight="bold" color="$danger11">{props.failedFiles || 0}</Text>
          </VStack>
          <VStack alignItems="end" spacing="$1">
            <Text color="$neutral11">预计剩余</Text>
            <Text fontWeight="bold">{formatTime(props.estimatedTime || 0)}</Text>
          </VStack>
        </HStack>
      </Box>

      {/* 警告信息 */}
      <Show when={showWarning()}>
        <Alert status="warning" w="$full">
          <AlertIcon />
          <Box>
            <AlertTitle>注意!</AlertTitle>
            <AlertDescription>
              有 {props.failedFiles} 个文件处理失败，建议检查网络连接或稍后重试。
            </AlertDescription>
          </Box>
        </Alert>
      </Show>

      {/* 主要操作按钮 */}
      <ButtonGroup w="$full" spacing="$2">
        <Show when={props.canPause && !props.isPaused}>
          <Button 
            colorScheme="warning" 
            variant="outline"
            onClick={props.onPause}
            flex="1"
          >
            ⏸️ 暂停
          </Button>
        </Show>
        
        <Show when={props.canResume && props.isPaused}>
          <Button 
            colorScheme="primary"
            onClick={props.onResume}
            flex="1"
          >
            ▶️ 继续
          </Button>
        </Show>
        
        <Show when={props.canCancel && !['complete', 'error', 'cancelled'].includes(props.phase)}>
          <Button 
            colorScheme="danger" 
            variant="outline"
            onClick={props.onCancel}
            flex="1"
          >
            ⏹️ 取消
          </Button>
        </Show>
        
        <Show when={props.canRetry && ['error', 'cancelled'].includes(props.phase)}>
          <Button 
            colorScheme="warning"
            onClick={props.onRetry}
            flex="1"
          >
            🔄 重试
          </Button>
        </Show>
        
        <Show when={['complete', 'error', 'cancelled'].includes(props.phase)}>
          <Button 
            colorScheme="info"
            onClick={props.onClose}
            flex="1"
          >
            ✓ 关闭
          </Button>
        </Show>
      </ButtonGroup>

      {/* 高级设置 */}
      <Box w="$full">
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced())}
          w="$full"
        >
          {showAdvanced() ? '隐藏高级设置 ▲' : '显示高级设置 ▼'}
        </Button>
        
        <Show when={showAdvanced()}>
          <VStack spacing="$4" mt="$3" p="$4" bg="$neutral1" rounded="$md">
            {/* 并发数设置 */}
            <FormControl>
              <FormLabel fontSize="sm">
                并发数
                <Tooltip label="同时处理的文件数量，数值越高速度越快但占用资源越多">
                  <Text as="span" ml="$1" color="$info11" cursor="help">ℹ️</Text>
                </Tooltip>
              </FormLabel>
              <HStack spacing="$2" alignItems="center">
                <Input
                  type="number"
                  value={props.concurrency || 1}
                  min={1}
                  max={props.maxConcurrency || 10}
                  step={1}
                  size="sm"
                  w="80px"
                  onChange={(e) => {
                    const value = parseInt(e.target.value)
                    if (!isNaN(value) && props.onConcurrencyChange) {
                      props.onConcurrencyChange(value)
                    }
                  }}
                />
                <Text fontSize="xs" color="$neutral11">
                  (1-{props.maxConcurrency || 10})
                </Text>
              </HStack>
            </FormControl>

            <Divider />

            {/* 开关设置 */}
            <VStack spacing="$3" w="$full">
              <FormControl display="flex" alignItems="center" justifyContent="space-between">
                <FormLabel fontSize="sm" mb="0">
                  自动重试失败文件
                  <Tooltip label="自动重试网络错误等可恢复的失败">
                    <Text as="span" ml="$1" color="$info11" cursor="help">ℹ️</Text>
                  </Tooltip>
                </FormLabel>
                <Switch
                  checked={props.enableAutoRetry}
                  onChange={props.onAutoRetryChange}
                  colorScheme="success"
                />
              </FormControl>

              <FormControl display="flex" alignItems="center" justifyContent="space-between">
                <FormLabel fontSize="sm" mb="0">
                  内存优化
                  <Tooltip label="启用内存监控和自动清理，降低内存使用">
                    <Text as="span" ml="$1" color="$info11" cursor="help">ℹ️</Text>
                  </Tooltip>
                </FormLabel>
                <Switch
                  checked={props.enableMemoryOptimization}
                  onChange={props.onMemoryOptimizationChange}
                  colorScheme="info"
                />
              </FormControl>
            </VStack>

            {/* 性能建议 */}
            <Box w="$full" p="$3" bg="$info2" rounded="$md" border="1px solid $info6">
              <Text fontSize="sm" fontWeight="bold" color="$info11" mb="$2">
                💡 性能建议
              </Text>
              <VStack alignItems="start" spacing="$1" fontSize="xs" color="$info11">
                <Text>• 网络较慢时建议降低并发数到 1-2</Text>
                <Text>• 内存不足时启用内存优化</Text>
                <Text>• 大量小文件时可适当提高并发数</Text>
                <Text>• 服务器响应慢时启用自动重试</Text>
              </VStack>
            </Box>
          </VStack>
        </Show>
      </Box>
    </VStack>
  )
}
