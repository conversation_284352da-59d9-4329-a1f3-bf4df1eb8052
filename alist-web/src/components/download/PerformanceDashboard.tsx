import { createSignal, createEffect, Show, For, onCleanup } from "solid-js"
import {
  Box,
  HStack,
  VStack,
  Text,
  Progress,
  Badge,
  Button,
  Grid,
  GridItem,
  Tooltip,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tabs,
  TabList,
  Tab,
  TabPanel,
  Switch,
  FormControl,
  FormLabel
} from "@hope-ui/solid"
import type {
  PerformanceMetrics,
  OptimizationSuggestion,
  PerformanceTrend
} from "~/utils/performance-monitor"

// 延迟导入性能监控模块
let performanceMonitor: any = null

const initPerformanceMonitor = async () => {
  if (!performanceMonitor) {
    const module = await import("~/utils/performance-monitor")
    performanceMonitor = module.performanceMonitor
  }
  return performanceMonitor
}

// 性能仪表板属性
export interface PerformanceDashboardProps {
  isVisible: boolean
  onClose?: () => void
  compact?: boolean
}

// 格式化字节
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化百分比
const formatPercentage = (value: number): string => {
  return `${(value * 100).toFixed(1)}%`
}

// 获取性能等级颜色
const getPerformanceColor = (value: number, thresholds: { warning: number, critical: number }) => {
  if (value > thresholds.critical) return 'danger'
  if (value > thresholds.warning) return 'warning'
  return 'success'
}

// 获取趋势图标
const getTrendIcon = (trend: 'improving' | 'stable' | 'degrading') => {
  switch (trend) {
    case 'improving': return '📈'
    case 'degrading': return '📉'
    default: return '➡️'
  }
}

// 性能指标卡片
const MetricCard = (props: {
  title: string
  value: string
  percentage?: number
  color?: string
  trend?: PerformanceTrend
  icon?: string
}) => {
  return (
    <Box
      p="$4"
      border="1px solid"
      borderColor="$neutral6"
      borderRadius="$md"
      bg="$loContrast"
      shadow="$sm"
    >
      {/* Header */}
      <HStack justifyContent="space-between" alignItems="center" mb="$3">
        <Text fontSize="sm" fontWeight="medium" color="$neutral11">
          {props.title}
        </Text>
        <Show when={props.icon}>
          <Text fontSize="lg">{props.icon}</Text>
        </Show>
      </HStack>

      {/* Body */}
      <VStack alignItems="start" spacing="$2">
        <HStack alignItems="end" spacing="$2">
          <Text fontSize="xl" fontWeight="bold" color={props.color}>
            {props.value}
          </Text>
          <Show when={props.trend}>
            <Tooltip label={`趋势: ${props.trend!.trend}`}>
              <Text fontSize="sm">
                {getTrendIcon(props.trend!.trend)}
              </Text>
            </Tooltip>
          </Show>
        </HStack>
        <Show when={props.percentage !== undefined}>
          <Progress
            value={props.percentage!}
            size="sm"
            colorScheme={props.color}
            w="$full"
          />
        </Show>
      </VStack>
    </Box>
  )
}

// 优化建议卡片
const SuggestionCard = (props: {
  suggestion: OptimizationSuggestion
  onApply: () => void
}) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'danger'
      case 'warning': return 'warning'
      default: return 'info'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'danger'
      case 'medium': return 'warning'
      default: return 'success'
    }
  }

  return (
    <Alert status={getSeverityColor(props.suggestion.severity)} variant="left-accent">
      <AlertIcon />
      <Box flex="1">
        <HStack justifyContent="space-between" alignItems="start" mb="$2">
          <VStack alignItems="start" spacing="$1" flex="1">
            <AlertTitle fontSize="sm">{props.suggestion.title}</AlertTitle>
            <AlertDescription fontSize="xs">
              {props.suggestion.description}
            </AlertDescription>
            <Text fontSize="xs" color="$neutral11">
              建议操作: {props.suggestion.action}
            </Text>
          </VStack>
          <VStack alignItems="end" spacing="$1">
            <Badge 
              colorScheme={getImpactColor(props.suggestion.impact)}
              size="sm"
            >
              {props.suggestion.impact} 影响
            </Badge>
            <Show when={props.suggestion.autoApplicable}>
              <Button
                size="xs"
                colorScheme={getSeverityColor(props.suggestion.severity)}
                onClick={props.onApply}
              >
                应用
              </Button>
            </Show>
          </VStack>
        </HStack>
      </Box>
    </Alert>
  )
}

// 实时图表组件（简化版）
const RealTimeChart = (props: {
  title: string
  data: number[]
  color: string
  unit: string
}) => {
  const maxValue = Math.max(...props.data, 1)
  const points = props.data.map((value, index) => {
    const x = (index / (props.data.length - 1)) * 100
    const y = 100 - (value / maxValue) * 100
    return `${x},${y}`
  }).join(' ')

  return (
    <Box
      p="$4"
      border="1px solid"
      borderColor="$neutral6"
      borderRadius="$md"
      bg="$loContrast"
      shadow="$sm"
    >
      {/* Header */}
      <Text fontSize="sm" fontWeight="medium" mb="$3">{props.title}</Text>

      {/* Body */}
      <Box position="relative" h="$20" w="$full">
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            fill="none"
            stroke={`var(--hope-colors-${props.color}9)`}
            stroke-width="2"
            points={points}
          />
        </svg>
        <Text
          position="absolute"
          top="$1"
          right="$1"
          fontSize="xs"
          color="$neutral11"
        >
          {props.data[props.data.length - 1]?.toFixed(1)} {props.unit}
        </Text>
      </Box>
    </Box>
  )
}

export const PerformanceDashboard = (props: PerformanceDashboardProps) => {
  // 创建默认的空指标
  const createEmptyMetrics = (): PerformanceMetrics => ({
    timestamp: Date.now(),
    memoryUsage: 0,
    memoryUsed: 0,
    memoryTotal: 0,
    downloadSpeed: 0,
    uploadSpeed: 0,
    networkLatency: 0,
    networkErrors: 0,
    processingSpeed: 0,
    queueSize: 0,
    activeConnections: 0,
    cpuUsage: 0,
    frameRate: 0,
    responseTime: 0,
    errorRate: 0,
    retryRate: 0,
    successRate: 1
  })

  const [metrics, setMetrics] = createSignal<PerformanceMetrics>(createEmptyMetrics())
  const [suggestions, setSuggestions] = createSignal<OptimizationSuggestion[]>([])
  const [autoOptimize, setAutoOptimize] = createSignal(false)
  const [showAdvanced, setShowAdvanced] = createSignal(false)
  const [isInitialized, setIsInitialized] = createSignal(false)

  // 历史数据用于图表
  const [memoryHistory, setMemoryHistory] = createSignal<number[]>([])
  const [speedHistory, setSpeedHistory] = createSignal<number[]>([])
  const [errorHistory, setErrorHistory] = createSignal<number[]>([])

  // 监控性能指标
  createEffect(() => {
    if (!props.isVisible) return

    // 初始化性能监控
    initPerformanceMonitor().then((monitor) => {
      if (!monitor) return

      const updateMetrics = (newMetrics: PerformanceMetrics) => {
        setMetrics(newMetrics)
        setSuggestions(monitor.getSuggestions())

        // 更新历史数据
        setMemoryHistory(prev => [...prev.slice(-29), newMetrics.memoryUsage])
        setSpeedHistory(prev => [...prev.slice(-29), newMetrics.downloadSpeed / 1024 / 1024]) // MB/s
        setErrorHistory(prev => [...prev.slice(-29), newMetrics.errorRate])
      }

      monitor.onMetricsUpdate('dashboard', updateMetrics)

      // 初始加载
      updateMetrics(monitor.getCurrentMetrics())
      setSuggestions(monitor.getSuggestions())
      setIsInitialized(true)

      onCleanup(() => {
        monitor.offMetricsUpdate('dashboard')
      })
    }).catch(error => {
      console.warn('Failed to initialize performance monitor:', error)
    })
  })

  // 自动优化
  createEffect(() => {
    if (autoOptimize() && isInitialized()) {
      initPerformanceMonitor().then((monitor) => {
        if (!monitor) return

        const criticalSuggestions = suggestions().filter(s =>
          s.severity === 'critical' && s.autoApplicable
        )

        criticalSuggestions.forEach(suggestion => {
          monitor.applySuggestion(suggestion.id)
        })
      })
    }
  })

  // 应用建议
  const applySuggestion = (suggestionId: string) => {
    initPerformanceMonitor().then((monitor) => {
      if (!monitor) return
      monitor.applySuggestion(suggestionId)
      setSuggestions(monitor.getSuggestions())
    })
  }

  // 导出性能报告
  const exportReport = () => {
    initPerformanceMonitor().then((monitor) => {
      if (!monitor) return
      const report = monitor.generateReport()
      const blob = new Blob([report], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance_report_${new Date().toISOString().slice(0, 19)}.json`
      a.click()
      URL.revokeObjectURL(url)
    })
  }

  // 获取趋势数据（安全版本）
  const getTrend = (metric: keyof PerformanceMetrics) => {
    if (!isInitialized()) {
      return {
        metric,
        values: [],
        timestamps: [],
        trend: 'stable' as const,
        changeRate: 0
      }
    }

    // 这里可以添加本地计算的趋势逻辑
    return {
      metric,
      values: [],
      timestamps: [],
      trend: 'stable' as const,
      changeRate: 0
    }
  }

  const currentMetrics = metrics()
  const memoryTrend = getTrend('memoryUsage')
  const speedTrend = getTrend('downloadSpeed')
  const errorTrend = getTrend('errorRate')

  return (
    <Show when={props.isVisible}>
      <Box
        w={props.compact ? "$80" : "$full"}
        maxW="$6xl"
        p="$4"
        bg="$loContrast"
        rounded="$lg"
        border="1px solid"
        borderColor="$neutral6"
        css={{
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }}
      >
        <VStack spacing="$4" w="$full">
          {/* 头部 */}
          <HStack w="$full" justifyContent="space-between" alignItems="center">
            <Text fontSize="lg" fontWeight="bold">
              性能监控仪表板
            </Text>
            <HStack spacing="$2">
              <FormControl display="flex" alignItems="center">
                <FormLabel fontSize="sm" mb="0" mr="$2">
                  自动优化
                </FormLabel>
                <Switch
                  checked={autoOptimize()}
                  onChange={setAutoOptimize}
                  colorScheme="success"
                  size="sm"
                />
              </FormControl>
              <Button size="xs" variant="ghost" onClick={exportReport}>
                📊 导出报告
              </Button>
              <Show when={props.onClose}>
                <Button size="xs" variant="ghost" onClick={props.onClose}>
                  ✕
                </Button>
              </Show>
            </HStack>
          </HStack>

          <Tabs defaultIndex={0} w="$full">
            <TabList>
              <Tab>实时监控</Tab>
              <Tab>性能趋势</Tab>
              <Tab>优化建议</Tab>
              <Show when={showAdvanced()}>
                <Tab>高级分析</Tab>
              </Show>
            </TabList>

            {/* 实时监控标签页 */}
            <TabPanel>
                <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="$4">
                  <GridItem>
                    <MetricCard
                      title="内存使用"
                      value={formatPercentage(currentMetrics.memoryUsage)}
                      percentage={currentMetrics.memoryUsage * 100}
                      color={getPerformanceColor(currentMetrics.memoryUsage, { warning: 0.7, critical: 0.85 })}
                      trend={memoryTrend}
                      icon="🧠"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <MetricCard
                      title="下载速度"
                      value={formatBytes(currentMetrics.downloadSpeed) + '/s'}
                      color="primary"
                      trend={speedTrend}
                      icon="⬇️"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <MetricCard
                      title="处理速度"
                      value={`${currentMetrics.processingSpeed.toFixed(1)} 文件/s`}
                      color="success"
                      icon="⚡"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <MetricCard
                      title="错误率"
                      value={formatPercentage(currentMetrics.errorRate)}
                      percentage={currentMetrics.errorRate * 100}
                      color={getPerformanceColor(currentMetrics.errorRate, { warning: 0.05, critical: 0.1 })}
                      trend={errorTrend}
                      icon="❌"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <MetricCard
                      title="队列大小"
                      value={currentMetrics.queueSize.toString()}
                      color={getPerformanceColor(currentMetrics.queueSize / 100, { warning: 0.5, critical: 0.8 })}
                      icon="📋"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <MetricCard
                      title="成功率"
                      value={formatPercentage(currentMetrics.successRate)}
                      percentage={currentMetrics.successRate * 100}
                      color="success"
                      icon="✅"
                    />
                  </GridItem>
                </Grid>
              </TabPanel>

              {/* 性能趋势标签页 */}
              <TabPanel>
                <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap="$4">
                  <GridItem>
                    <RealTimeChart
                      title="内存使用趋势"
                      data={memoryHistory().map(v => v * 100)}
                      color="danger"
                      unit="%"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <RealTimeChart
                      title="下载速度趋势"
                      data={speedHistory()}
                      color="primary"
                      unit="MB/s"
                    />
                  </GridItem>
                  
                  <GridItem>
                    <RealTimeChart
                      title="错误率趋势"
                      data={errorHistory().map(v => v * 100)}
                      color="warning"
                      unit="%"
                    />
                  </GridItem>
                </Grid>
              </TabPanel>

              {/* 优化建议标签页 */}
              <TabPanel>
                <VStack spacing="$3" w="$full">
                  <Show 
                    when={suggestions().length > 0}
                    fallback={
                      <Box textAlign="center" py="$8">
                        <Text fontSize="lg" color="$success11" mb="$2">
                          🎉 系统运行良好
                        </Text>
                        <Text fontSize="sm" color="$neutral11">
                          当前没有性能优化建议
                        </Text>
                      </Box>
                    }
                  >
                    <For each={suggestions()}>
                      {(suggestion) => (
                        <SuggestionCard
                          suggestion={suggestion}
                          onApply={() => applySuggestion(suggestion.id)}
                        />
                      )}
                    </For>
                  </Show>
                </VStack>
              </TabPanel>

              {/* 高级分析标签页 */}
              <Show when={showAdvanced()}>
                <TabPanel>
                  <VStack spacing="$4" w="$full">
                    <Text fontSize="md" fontWeight="bold">详细性能指标</Text>
                    
                    <Grid templateColumns="repeat(2, 1fr)" gap="$4" w="$full">
                      <GridItem>
                        <VStack alignItems="start" spacing="$2">
                          <Text fontSize="sm" fontWeight="medium">系统指标</Text>
                          <Text fontSize="xs">CPU使用率: {formatPercentage(currentMetrics.cpuUsage)}</Text>
                          <Text fontSize="xs">帧率: {currentMetrics.frameRate.toFixed(1)} fps</Text>
                          <Text fontSize="xs">响应时间: {currentMetrics.responseTime.toFixed(1)} ms</Text>
                        </VStack>
                      </GridItem>
                      
                      <GridItem>
                        <VStack alignItems="start" spacing="$2">
                          <Text fontSize="sm" fontWeight="medium">网络指标</Text>
                          <Text fontSize="xs">网络延迟: {currentMetrics.networkLatency.toFixed(1)} ms</Text>
                          <Text fontSize="xs">网络错误: {currentMetrics.networkErrors}</Text>
                          <Text fontSize="xs">活动连接: {currentMetrics.activeConnections}</Text>
                        </VStack>
                      </GridItem>
                    </Grid>
                  </VStack>
                </TabPanel>
              </Show>
          </Tabs>

          {/* 底部控制 */}
          <HStack w="$full" justifyContent="space-between" alignItems="center">
            <Text fontSize="xs" color="$neutral11">
              最后更新: {new Date(currentMetrics.timestamp).toLocaleTimeString()}
            </Text>
            <Button
              size="xs"
              variant="ghost"
              onClick={() => setShowAdvanced(!showAdvanced())}
            >
              {showAdvanced() ? '隐藏高级功能' : '显示高级功能'}
            </Button>
          </HStack>
        </VStack>
      </Box>
    </Show>
  )
}
