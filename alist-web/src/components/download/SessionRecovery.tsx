import { createSignal, Show, For, createMemo } from "solid-js"
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  ButtonGroup,
  Badge,
  Box,
  Progress,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tooltip,
  IconButton
} from "@hope-ui/solid"
import { useT } from "~/hooks"
import { DownloadSession, downloadStateManager } from "~/utils/download-state"

// 会话恢复组件属性
export interface SessionRecoveryProps {
  isOpen: boolean
  onClose: () => void
  onResumeSession: (session: DownloadSession) => void
  onStartNew: () => void
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)

  if (diffDays > 0) {
    return `${diffDays}天前`
  } else if (diffHours > 0) {
    return `${diffHours}小时前`
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    return diffMinutes > 0 ? `${diffMinutes}分钟前` : '刚刚'
  }
}

// 格式化文件大小
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化持续时间
const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'info'
    case 'paused': return 'warning'
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'cancelled': return 'neutral'
    default: return 'neutral'
  }
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active': return '▶️'
    case 'paused': return '⏸️'
    case 'completed': return '✅'
    case 'failed': return '❌'
    case 'cancelled': return '⏹️'
    default: return '❓'
  }
}

// 单个会话项组件
const SessionItem = (props: {
  session: DownloadSession
  onResume: () => void
  onDelete: () => void
  onExport: () => void
}) => {
  const t = useT()
  const [showDetails, setShowDetails] = createSignal(false)

  const completionRate = createMemo(() => {
    return props.session.totalFiles > 0 
      ? Math.round((props.session.completedFiles / props.session.totalFiles) * 100)
      : 0
  })

  const stats = createMemo(() => {
    return downloadStateManager.getSessionStats(props.session.id)
  })

  const canResume = createMemo(() => {
    return ['active', 'paused', 'failed'].includes(props.session.status)
  })

  return (
    <Box
      w="$full"
      p="$4"
      bg="$neutral2"
      rounded="$lg"
      border="1px solid"
      borderColor="$neutral6"
      css={{
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '$neutral8',
          transform: 'translateY(-1px)',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }
      }}
    >
      <VStack spacing="$3" alignItems="start">
        {/* 会话头部 */}
        <HStack w="$full" justifyContent="space-between" alignItems="start">
          <HStack spacing="$3" flex="1">
            <Text fontSize="lg">{getStatusIcon(props.session.status)}</Text>
            <VStack alignItems="start" spacing="$1" flex="1">
              <Text fontSize="md" fontWeight="semibold" css={{ wordBreak: "break-all" }}>
                {props.session.name}
              </Text>
              <HStack spacing="$2" flexWrap="wrap">
                <Badge 
                  colorScheme={getStatusColor(props.session.status)}
                  variant="solid"
                  size="sm"
                >
                  {t(`download.session.status.${props.session.status}`)}
                </Badge>
                <Badge variant="outline" size="sm">
                  {props.session.totalFiles} 文件
                </Badge>
                <Badge variant="outline" size="sm">
                  {formatBytes(props.session.totalSize)}
                </Badge>
              </HStack>
            </VStack>
          </HStack>

          <VStack spacing="$1" alignItems="end">
            <Text fontSize="sm" color="$neutral11">
              {formatTime(props.session.lastUpdateTime)}
            </Text>
            <Text fontSize="lg" fontWeight="bold" color={getStatusColor(props.session.status)}>
              {completionRate()}%
            </Text>
          </VStack>
        </HStack>

        {/* 进度条 */}
        <Box w="$full">
          <Progress 
            value={completionRate()} 
            size="sm"
            colorScheme={getStatusColor(props.session.status)}
          />
        </Box>

        {/* 详细信息 */}
        <Show when={showDetails()}>
          <VStack spacing="$2" w="$full" p="$3" bg="$neutral1" rounded="$md">
            <HStack w="$full" justifyContent="space-between" fontSize="sm">
              <Text color="$neutral11">开始时间:</Text>
              <Text>{new Date(props.session.startTime).toLocaleString()}</Text>
            </HStack>
            <HStack w="$full" justifyContent="space-between" fontSize="sm">
              <Text color="$neutral11">持续时间:</Text>
              <Text>{formatDuration(stats()?.duration || 0)}</Text>
            </HStack>
            <HStack w="$full" justifyContent="space-between" fontSize="sm">
              <Text color="$neutral11">已完成:</Text>
              <Text color="$success11">{props.session.completedFiles}</Text>
            </HStack>
            <HStack w="$full" justifyContent="space-between" fontSize="sm">
              <Text color="$neutral11">失败:</Text>
              <Text color="$danger11">{props.session.failedFiles}</Text>
            </HStack>
            <HStack w="$full" justifyContent="space-between" fontSize="sm">
              <Text color="$neutral11">已下载:</Text>
              <Text>{formatBytes(props.session.downloadedSize)}</Text>
            </HStack>
            <Show when={stats()?.avgSpeed}>
              <HStack w="$full" justifyContent="space-between" fontSize="sm">
                <Text color="$neutral11">平均速度:</Text>
                <Text>{formatBytes(stats()!.avgSpeed)}/s</Text>
              </HStack>
            </Show>
          </VStack>
        </Show>

        {/* 操作按钮 */}
        <HStack w="$full" justifyContent="space-between">
          <Button
            size="xs"
            variant="ghost"
            onClick={() => setShowDetails(!showDetails())}
          >
            {showDetails() ? '收起详情 ▲' : '展开详情 ▼'}
          </Button>

          <HStack spacing="$1">
            <Tooltip label="导出会话数据">
              <IconButton
                size="xs"
                variant="ghost"
                aria-label="Export"
                onClick={props.onExport}
              >
                📤
              </IconButton>
            </Tooltip>

            <Tooltip label="删除会话">
              <IconButton
                size="xs"
                variant="ghost"
                colorScheme="danger"
                aria-label="Delete"
                onClick={props.onDelete}
              >
                🗑️
              </IconButton>
            </Tooltip>

            <Show when={canResume()}>
              <Button
                size="xs"
                colorScheme="primary"
                onClick={props.onResume}
              >
                {props.session.status === 'paused' ? '继续' : '恢复'}
              </Button>
            </Show>
          </HStack>
        </HStack>
      </VStack>
    </Box>
  )
}

export const SessionRecovery = (props: SessionRecoveryProps) => {
  const t = useT()
  const [sessions, setSessions] = createSignal<DownloadSession[]>([])
  const [loading, setLoading] = createSignal(false)

  // 加载会话列表
  const loadSessions = () => {
    setLoading(true)
    try {
      const allSessions = downloadStateManager.getAllSessions()
      setSessions(allSessions)
    } catch (error) {
      console.error('Failed to load sessions:', error)
    } finally {
      setLoading(false)
    }
  }

  // 组件打开时加载会话
  createSignal(() => {
    if (props.isOpen) {
      loadSessions()
    }
  })

  // 恢复会话
  const handleResumeSession = (session: DownloadSession) => {
    props.onResumeSession(session)
    props.onClose()
  }

  // 删除会话
  const handleDeleteSession = (sessionId: string) => {
    if (confirm('确定要删除这个下载会话吗？此操作无法撤销。')) {
      downloadStateManager.deleteSession(sessionId)
      loadSessions() // 重新加载列表
    }
  }

  // 导出会话
  const handleExportSession = (sessionId: string) => {
    const exportData = downloadStateManager.exportSession(sessionId)
    if (exportData) {
      const blob = new Blob([exportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `alist_session_${sessionId}.json`
      a.click()
      URL.revokeObjectURL(url)
    }
  }

  // 清理过期会话
  const handleCleanupSessions = () => {
    if (confirm('确定要清理过期的下载会话吗？')) {
      downloadStateManager.cleanupExpiredSessions()
      loadSessions()
    }
  }

  // 过滤会话
  const incompleteSessions = createMemo(() => {
    return sessions().filter(s => ['active', 'paused', 'failed'].includes(s.status))
  })

  const completedSessions = createMemo(() => {
    return sessions().filter(s => ['completed', 'cancelled'].includes(s.status))
  })

  return (
    <Modal isOpen={props.isOpen} onClose={props.onClose} size="2xl" isCentered>
      <ModalOverlay />
      <ModalContent maxH="80vh">
        <ModalHeader>
          下载会话恢复
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody overflowY="auto">
          <VStack spacing="$4" w="$full">
            {/* 提示信息 */}
            <Show when={incompleteSessions().length > 0}>
              <Alert status="info" w="$full">
                <AlertIcon />
                <Box>
                  <AlertTitle>发现未完成的下载!</AlertTitle>
                  <AlertDescription>
                    您有 {incompleteSessions().length} 个未完成的下载会话，可以选择恢复继续下载。
                  </AlertDescription>
                </Box>
              </Alert>
            </Show>

            {/* 未完成的会话 */}
            <Show when={incompleteSessions().length > 0}>
              <Box w="$full">
                <Text fontSize="lg" fontWeight="bold" mb="$3">
                  未完成的下载 ({incompleteSessions().length})
                </Text>
                <VStack spacing="$3">
                  <For each={incompleteSessions()}>
                    {(session) => (
                      <SessionItem
                        session={session}
                        onResume={() => handleResumeSession(session)}
                        onDelete={() => handleDeleteSession(session.id)}
                        onExport={() => handleExportSession(session.id)}
                      />
                    )}
                  </For>
                </VStack>
              </Box>
            </Show>

            {/* 分隔线 */}
            <Show when={incompleteSessions().length > 0 && completedSessions().length > 0}>
              <Divider />
            </Show>

            {/* 已完成的会话 */}
            <Show when={completedSessions().length > 0}>
              <Box w="$full">
                <HStack justifyContent="space-between" alignItems="center" mb="$3">
                  <Text fontSize="lg" fontWeight="bold">
                    历史记录 ({completedSessions().length})
                  </Text>
                  <Button
                    size="xs"
                    variant="ghost"
                    colorScheme="neutral"
                    onClick={handleCleanupSessions}
                  >
                    清理过期
                  </Button>
                </HStack>
                <VStack spacing="$3">
                  <For each={completedSessions()}>
                    {(session) => (
                      <SessionItem
                        session={session}
                        onResume={() => handleResumeSession(session)}
                        onDelete={() => handleDeleteSession(session.id)}
                        onExport={() => handleExportSession(session.id)}
                      />
                    )}
                  </For>
                </VStack>
              </Box>
            </Show>

            {/* 空状态 */}
            <Show when={sessions().length === 0 && !loading()}>
              <Box textAlign="center" py="$8">
                <Text fontSize="lg" color="$neutral11" mb="$2">
                  📁 暂无下载会话
                </Text>
                <Text fontSize="sm" color="$neutral10">
                  开始新的下载后，会话信息将显示在这里
                </Text>
              </Box>
            </Show>

            {/* 加载状态 */}
            <Show when={loading()}>
              <Box textAlign="center" py="$8">
                <Text>加载中...</Text>
              </Box>
            </Show>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <ButtonGroup spacing="$2">
            <Button variant="outline" onClick={props.onClose}>
              取消
            </Button>
            <Button colorScheme="primary" onClick={props.onStartNew}>
              开始新下载
            </Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
