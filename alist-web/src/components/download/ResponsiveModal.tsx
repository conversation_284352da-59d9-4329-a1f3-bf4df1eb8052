import { createSignal, createEffect, Show, JSX } from "solid-js"
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  Box,
  useBreakpointValue
} from "@hope-ui/solid"

// 响应式模态框属性
export interface ResponsiveModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: JSX.Element
  size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  isCentered?: boolean
  closeOnOverlayClick?: boolean
  closeOnEsc?: boolean
}

export const ResponsiveModal = (props: ResponsiveModalProps) => {
  // 响应式尺寸
  const modalSize = useBreakpointValue({
    base: "full", // 移动端全屏
    sm: "lg",     // 小屏幕
    md: "xl",     // 中等屏幕
    lg: "2xl",    // 大屏幕
    xl: "2xl"     // 超大屏幕
  })

  // 响应式内边距
  const modalPadding = useBreakpointValue({
    base: "$2",   // 移动端小内边距
    sm: "$4",     // 小屏幕
    md: "$6",     // 中等屏幕
    lg: "$8"      // 大屏幕
  })

  return (
    <Modal
      isOpen={props.isOpen}
      onClose={props.onClose}
      size={props.size || modalSize}
      isCentered={props.isCentered ?? true}
      closeOnOverlayClick={props.closeOnOverlayClick ?? false}
      closeOnEsc={props.closeOnEsc ?? true}
    >
      <ModalOverlay />
      <ModalContent
        mx={modalPadding}
        my={modalPadding}
        maxH="90vh"
        css={{
          '@media (max-width: 768px)': {
            margin: '$2',
            maxHeight: 'calc(100vh - 16px)',
            borderRadius: '$lg'
          }
        }}
      >
        <ModalHeader
          fontSize={{ base: "lg", md: "xl" }}
          py={{ base: "$3", md: "$4" }}
          px={{ base: "$4", md: "$6" }}
          borderBottom="1px solid"
          borderColor="$neutral6"
        >
          {props.title}
        </ModalHeader>
        
        <ModalCloseButton
          size={{ base: "sm", md: "md" }}
          top={{ base: "$3", md: "$4" }}
          right={{ base: "$3", md: "$4" }}
        />
        
        <Box
          overflowY="auto"
          css={{
            '&::-webkit-scrollbar': {
              width: '6px'
            },
            '&::-webkit-scrollbar-track': {
              background: 'var(--hope-colors-neutral3)'
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'var(--hope-colors-neutral6)',
              borderRadius: '3px'
            }
          }}
        >
          {props.children}
        </Box>
      </ModalContent>
    </Modal>
  )
}

// 移动端优化的进度模态框
export const MobileOptimizedModal = (props: ResponsiveModalProps) => {
  const [isFullscreen, setIsFullscreen] = createSignal(false)
  
  // 检测是否为移动设备
  const isMobile = useBreakpointValue({
    base: true,
    md: false
  })

  createEffect(() => {
    if (isMobile) {
      setIsFullscreen(true)
    }
  })

  return (
    <Modal
      isOpen={props.isOpen}
      onClose={props.onClose}
      size={isFullscreen() ? "full" : "xl"}
      isCentered={!isFullscreen()}
      closeOnOverlayClick={false}
      closeOnEsc={true}
    >
      <ModalOverlay />
      <ModalContent
        h={isFullscreen() ? "100vh" : "auto"}
        maxH={isFullscreen() ? "100vh" : "90vh"}
        m={isFullscreen() ? "0" : "$4"}
        borderRadius={isFullscreen() ? "0" : "$lg"}
        css={{
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <ModalHeader
          fontSize="lg"
          py="$3"
          px="$4"
          borderBottom="1px solid"
          borderColor="$neutral6"
          flexShrink={0}
          position="sticky"
          top={0}
          bg="$loContrast"
          zIndex={1}
        >
          {props.title}
        </ModalHeader>
        
        <ModalCloseButton
          size="sm"
          top="$3"
          right="$3"
        />
        
        <Box
          flex={1}
          overflowY="auto"
          css={{
            '&::-webkit-scrollbar': {
              width: '4px'
            },
            '&::-webkit-scrollbar-track': {
              background: 'transparent'
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'var(--hope-colors-neutral6)',
              borderRadius: '2px'
            }
          }}
        >
          {props.children}
        </Box>
      </ModalContent>
    </Modal>
  )
}

// 自适应卡片容器
export const AdaptiveCard = (props: {
  children: JSX.Element
  title?: string
  collapsible?: boolean
  defaultExpanded?: boolean
}) => {
  const [isExpanded, setIsExpanded] = createSignal(props.defaultExpanded ?? true)
  
  const cardPadding = useBreakpointValue({
    base: "$3",
    md: "$4",
    lg: "$6"
  })

  const titleSize = useBreakpointValue({
    base: "sm",
    md: "md",
    lg: "lg"
  })

  return (
    <Box
      w="$full"
      bg="$neutral2"
      rounded={{ base: "$md", md: "$lg" }}
      border="1px solid"
      borderColor="$neutral6"
      overflow="hidden"
    >
      <Show when={props.title}>
        <Box
          p={cardPadding}
          borderBottom={isExpanded() ? "1px solid" : "none"}
          borderColor="$neutral6"
          bg="$neutral3"
          cursor={props.collapsible ? "pointer" : "default"}
          onClick={() => props.collapsible && setIsExpanded(!isExpanded())}
          css={{
            userSelect: 'none'
          }}
        >
          <Box
            fontSize={titleSize}
            fontWeight="semibold"
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            {props.title}
            <Show when={props.collapsible}>
              <Box
                transform={isExpanded() ? "rotate(180deg)" : "rotate(0deg)"}
                transition="transform 0.2s ease"
              >
                ▼
              </Box>
            </Show>
          </Box>
        </Box>
      </Show>
      
      <Show when={isExpanded()}>
        <Box p={cardPadding}>
          {props.children}
        </Box>
      </Show>
    </Box>
  )
}

// 响应式网格布局
export const ResponsiveGrid = (props: {
  children: JSX.Element[]
  columns?: { base?: number, sm?: number, md?: number, lg?: number, xl?: number }
  spacing?: string
}) => {
  const gridColumns = useBreakpointValue({
    base: props.columns?.base || 1,
    sm: props.columns?.sm || 2,
    md: props.columns?.md || 3,
    lg: props.columns?.lg || 4,
    xl: props.columns?.xl || 4
  })

  return (
    <Box
      display="grid"
      gridTemplateColumns={`repeat(${gridColumns}, 1fr)`}
      gap={props.spacing || "$4"}
      w="$full"
    >
      {props.children}
    </Box>
  )
}
