{"obj": {"name": "Name", "size": "Size", "modified": "Modified", "type": "File Type"}, "preview": {"download": "Download", "failed_load_img": "Failed to load image", "open_with": "Open with ...", "play_with": "Play with ...", "install": "Install", "installing": "Installing", "tr-install": "TrollStore", "tr-installing": "TrollStore Installing", "open_in_new_window": "Open in new window", "auto_next": "Auto next"}, "layouts": {"list": "List View", "grid": "Grid View", "image": "Image View"}, "no_images": "No images in the current folder", "load_more": "Load more", "no_more": "No more", "input_password": "Please input password", "toolbar": {"more": "More", "refresh": "Refresh", "toggle_theme": "Toggle Theme", "switch_lang": "Switch Language", "toggle_markdown_toc": "Toggle Outline", "toggle_checkbox": "Toggle Checkbox", "select_all": "Select All", "rename": "<PERSON><PERSON>", "input_new_name": "Input new name", "move": "Move", "copy": "Copy", "choose_dst_folder": "Select destination folder", "delete": "Delete", "delete-tips": "Are you sure to delete the selected object?", "decompress": "Decompress", "decompress-pass": "Extraction password: ", "decompress-cache-full": "Cache full into a temp-file", "decompress-put-into-new": "Decompress into a new sub-folder", "extract": "Extract to", "archive": {"input_password": "Please input extraction password", "incorrect_password": "Incorrect password", "extract_header": "Extract {{path}} to..."}, "copy_link": "Copy link", "preview_page": "Preview page", "down_link": "Download link", "encode_down_link": "Encode download link", "mkdir": "Folder", "input_dir_name": "Input folder name", "new_file": "New File", "input_filename": "Input filename", "cancel_select": "Cancel Select", "offline_download": "Offline download", "offline_download-tips": "One URL per line", "delete_policy": {"delete_on_upload_succeed": "Delete on upload succeed", "delete_on_upload_failed": "Delete on upload failed", "delete_never": "Never delete", "delete_always": "Always delete"}, "download": "Download", "batch_download": "Batch Download", "package_download": "Package Download", "package_download_disabled": "Package download is disabled", "send_aria2": "Send to Aria2", "aria2_not_set": "Please set aria2 rpc url", "send_aria2_success": "Send to aria2 successfully", "pre_package_download-tips": "Using streamsaver in the browser instead of the server for package download requires the corresponding storage to support cors, and the unsupported storage will fail.", "package_download-tips": "Downloading, please wait don't close the page", "playlist_download": "Playlist Download", "upload": "Upload", "share_transfer_text": "Share Transfer", "local_settings": "Local Settings", "recursive_move": "Recursive Move", "recursive_move_directory-tips": "Are you sure you want to move all files in the current folder and its subfolders to the specified folder?", "remove_empty_directory": "Remove Empty Folder", "remove_empty_directory-tips": "Are you sure to delete all its empty subfolders?", "batch_rename": "<PERSON><PERSON>", "regex_rename": "Regex Rename", "sequential_renaming": "Sequential Rename", "regex_rename_preview": "Changed Files", "regex_rename_preview_old_name": "Old Name", "regex_rename_preview_new_name": "New Name", "regular_rename": "Regular expression file renaming. Input the source file name regular expression on the first line, and input the new file name regular expression on the second line.", "sequential_renaming_desc": "The new file name will have a numerical starting value appended to it, and it will be displayed sequentially by adding 1 to the starting value. Enter the new file name on the first line and the starting value on the second line.", "search": "Search", "selected_count": "{{folders}} folders / {{files}} files", "share_transfer": {"title": "Baidu Netdisk Batch Transfer", "usage_title": "Usage Instructions", "usage_desc": "Support batch transfer of Baidu Netdisk share links, up to 10 links at a time. Baidu Netdisk Cookie is required.", "baidu_cookie": "Baidu Netdisk Cookie *", "baidu_cookie_placeholder": "Please enter the complete Baidu Netdisk Cookie (after logging into Baidu Netdisk, press F12 to open developer tools, refresh the page in the Network tab, find the main request, and copy the Cookie)", "share_links": "Share Links * (up to 10)", "share_links_placeholder": "Please enter Baidu Netdisk share links, one per line, supported formats:\nhttps://pan.baidu.com/s/1nvBwS25lENYceUu3OMH4tg 6img\nhttps://pan.baidu.com/s/1nvBwS25lENYceUu3OMH4tg?pwd=6img\nhttps://pan.baidu.com/s/1nvBwS25lENYceUu3OMH4tg Extract code: 6img", "current_links_count": "Current link count:", "target_path": "Target Path *", "target_path_placeholder": "Please enter the transfer target path, e.g.: /downloads", "create_folder": "Auto create \"Baidu Share\" folder", "transferring": "Transferring...", "transfer_results": "Transfer Results:", "reset": "Reset", "start_transfer": "Start Transfer", "error_no_links": "Please enter at least one share link", "error_too_many_links": "Cannot transfer more than 10 links at once", "error_no_cookie": "Please enter Baidu Netdisk Cookie", "error_invalid_cookie": "Invalid Baidu Netdisk Cookie format, please ensure it contains complete Cookie information", "error_no_path": "Please enter target path"}}, "upload": {"add_as_task": "Add as task", "try_rapid": "Try rapid", "upload-tips": "Drag files here to upload, or click:", "release": "Release to upload", "no_files_drag": "No files were dragged in.", "upload_files": "<PERSON><PERSON>", "upload_folder": "Choose Folder", "pending": "Pending", "uploading": "Uploading", "backending": "Uploading in the backend", "success": "Success", "error": "Error", "back": "Back to Upload", "clear_done": "Clear Done"}, "local_settings": {"aria2_rpc_url": "Aria2 RPC URL", "aria2_rpc_secret": "Aria2 RPC secret", "aria2_dir": "Aria2 download directory", "show_folder_in_image_view": "Show folder in image view", "show_folder_in_image_view_options": {"top": "Top", "bottom": "Bottom", "none": "None"}, "global_default_layout": "Global default layout", "global_default_layout_options": {"list": "List View", "grid": "Grid View", "image": "Image View"}, "position_of_header_navbar": "Position of header & nav bar", "position_of_header_navbar_options": {"static": "Normal", "sticky": "Stick to top of page", "only_navbar_sticky": "Only nav bar sticky"}, "show_sidebar": "Show sidebar", "show_sidebar_options": {"none": "None", "visible": "Visible"}, "show_home_sidebar": "Show home sidebar", "show_home_sidebar_options": {"none": "None", "visible": "Visible"}, "list_item_filename_overflow": "List item filename overflow", "list_item_filename_overflow_options": {"ellipsis": "El<PERSON><PERSON>", "scrollable": "Scrollable", "multi_line": "Multi-line"}, "open_item_on_checkbox": "Open item on Checkbox", "open_item_on_checkbox_options": {"direct": "Direct", "dblclick": "Double click", "disable_while_checked": "Disable while checked"}}, "package_download": {"current_status": "Current Status", "initializing": "Initializing", "fetching_struct": "Fetching folder structure", "fetching_struct_failed": "Failed to fetch folder structure", "downloading": "Downloading files, don't close or refresh the page", "failed": "Failed to package download", "success": "Download completed", "manager_not_ready": "Download manager not ready", "no_files_found": "No files found to download", "retry_failed": "Retry failed files", "progress": "Progress", "size": "Size", "current_file": "Current file", "failed_files": "Failed files", "advanced_info": "Advanced Information", "paused": "Download paused", "resumed": "Download resumed", "estimated_time": "Estimated time", "processing_speed": "Processing speed", "session_recovery": "Session Recovery", "resume_session": "Resume Session", "start_new": "Start New Download", "phase": {"scanning": "Scanning", "downloading": "Downloading", "compressing": "Compressing", "complete": "Complete", "error": "Error", "cancelled": "Cancelled"}}, "download": {"session": {"status": {"active": "Active", "paused": "Paused", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}}}, "footer": {"powered_by": "Powered by AList", "manage": "Manage"}, "search": {"search": "Search", "no_result": "No result yet", "scopes": {"all": "All", "folder": "Folder", "file": "File"}}, "fetching_settings_failed": "Failed fetching settings: ", "get_current_user_failed": "Failed get current user: ", "Loading storage, please wait": "Loading storage, please wait", "conflict_policy": {"cancel_if_exists": "Cancel if file exists", "overwrite_existing": "Overwrite existing files", "skip_existing": "Skip existing files"}, "sidemenu": {"my_files": "My Files", "shared_files": "Shared Files", "my_storage": "My Storage", "background_tasks": "Background Tasks"}}