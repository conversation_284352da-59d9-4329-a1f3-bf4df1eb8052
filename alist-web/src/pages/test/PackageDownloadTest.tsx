import { createSignal, onMount, Show } from "solid-js"
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Progress,
  Heading,
  Badge
} from "@hope-ui/solid"
import { useRouter, useLocation } from "@solidjs/router"
import { notify } from "~/utils"
import { fsList, getLinkByDirAndObj } from "~/store"

// 简化的文件信息接口
interface TestFileInfo {
  path: string
  url: string
  size: number
  name: string
}

// 简化的进度信息接口
interface TestProgress {
  phase: 'ready' | 'scanning' | 'downloading' | 'compressing' | 'complete' | 'error'
  scannedFiles: number
  totalFiles: number
  downloadedFiles: number
  currentFile: string
  percentage: number
}

export default function PackageDownloadTest() {
  const [progress, setProgress] = createSignal<TestProgress>({
    phase: 'ready',
    scannedFiles: 0,
    totalFiles: 0,
    downloadedFiles: 0,
    currentFile: '',
    percentage: 0
  })

  const [logs, setLogs] = createSignal<string[]>([])
  const [testFiles, setTestFiles] = createSignal<TestFileInfo[]>([])
  const [isRunning, setIsRunning] = createSignal(false)

  // 添加日志的辅助函数
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] ${message}`
    console.log(logMessage)
    setLogs(prev => [...prev, logMessage])
  }

  // 测试用的模拟文件对象
  const mockSelectedObjs = [
    {
      name: "test-folder",
      is_dir: true,
      size: 0,
      modified: new Date(),
      type: 1
    },
    {
      name: "test-file.txt",
      is_dir: false,
      size: 1024,
      modified: new Date(),
      type: 2
    }
  ]

  // 获取当前路径
  const getCurrentPath = () => {
    try {
      const location = useLocation()
      const path = location.pathname.replace('/test/package-download', '') || '/virtual/storage/1'
      addLog(`📍 当前路径: ${path}`)
      return path
    } catch (error) {
      addLog(`❌ 获取路径失败: ${error}`)
      return '/virtual/storage/1'
    }
  }

  // 测试文件列表API
  const testFsList = async (path: string) => {
    try {
      addLog(`🔍 测试fsList API: ${path}`)
      const result = await fsList(path, '')
      addLog(`✅ fsList响应: code=${result.code}, message=${result.message}`)
      if (result.data && result.data.content) {
        addLog(`📋 找到 ${result.data.content.length} 个项目`)
        result.data.content.slice(0, 3).forEach((item: any, index: number) => {
          addLog(`  ${index + 1}. ${item.name} (${item.is_dir ? '文件夹' : '文件'})`)
        })
      }
      return result
    } catch (error) {
      addLog(`❌ fsList API调用失败: ${error}`)
      throw error
    }
  }

  // 测试链接生成
  const testGetLink = (path: string, obj: any) => {
    try {
      addLog(`🔗 测试链接生成: ${obj.name}`)
      const link = getLinkByDirAndObj(path, obj, 'direct', true)
      addLog(`✅ 生成链接: ${link}`)
      return link
    } catch (error) {
      addLog(`❌ 链接生成失败: ${error}`)
      throw error
    }
  }

  // 简化的文件扫描逻辑
  const scanFiles = async () => {
    try {
      setIsRunning(true)
      addLog('🚀 开始文件扫描测试')
      
      const currentPath = getCurrentPath()
      const files: TestFileInfo[] = []
      
      setProgress(prev => ({
        ...prev,
        phase: 'scanning',
        scannedFiles: 0,
        totalFiles: 0,
        currentFile: '开始扫描...'
      }))

      for (let i = 0; i < mockSelectedObjs.length; i++) {
        const obj = mockSelectedObjs[i]
        addLog(`📁 扫描对象 ${i + 1}/${mockSelectedObjs.length}: ${obj.name}`)
        
        setProgress(prev => ({
          ...prev,
          scannedFiles: i + 1,
          currentFile: `扫描: ${obj.name}`
        }))

        if (!obj.is_dir) {
          // 处理文件
          try {
            const url = testGetLink(currentPath, obj)
            const fileInfo: TestFileInfo = {
              path: obj.name,
              url: url,
              size: obj.size,
              name: obj.name
            }
            files.push(fileInfo)
            addLog(`✅ 添加文件: ${obj.name}`)
          } catch (error) {
            addLog(`❌ 处理文件失败: ${obj.name} - ${error}`)
          }
        } else {
          // 处理文件夹
          try {
            const folderPath = `${currentPath}/${obj.name}`
            addLog(`📂 扫描文件夹: ${folderPath}`)
            
            const resp = await testFsList(folderPath)
            if (resp.code === 200 && resp.data && resp.data.content) {
              let folderFileCount = 0
              for (const item of resp.data.content) {
                if (!item.is_dir) {
                  try {
                    const url = testGetLink(folderPath, item)
                    const fileInfo: TestFileInfo = {
                      path: `${obj.name}/${item.name}`,
                      url: url,
                      size: item.size || 0,
                      name: item.name
                    }
                    files.push(fileInfo)
                    folderFileCount++
                    addLog(`  ✅ 添加文件: ${item.name}`)
                  } catch (error) {
                    addLog(`  ❌ 处理文件失败: ${item.name} - ${error}`)
                  }
                }
              }
              addLog(`✅ 文件夹扫描完成，找到 ${folderFileCount} 个文件`)
            } else {
              addLog(`⚠️ 文件夹响应异常: ${resp.message}`)
            }
          } catch (error) {
            addLog(`❌ 扫描文件夹失败: ${obj.name} - ${error}`)
          }
        }
      }

      setTestFiles(files)
      setProgress(prev => ({
        ...prev,
        phase: 'complete',
        totalFiles: files.length,
        scannedFiles: mockSelectedObjs.length,
        currentFile: `扫描完成，找到 ${files.length} 个文件`,
        percentage: 100
      }))

      addLog(`🎉 扫描完成！总共找到 ${files.length} 个文件`)
      
    } catch (error) {
      addLog(`❌ 扫描过程发生错误: ${error}`)
      setProgress(prev => ({
        ...prev,
        phase: 'error',
        currentFile: `错误: ${error}`
      }))
    } finally {
      setIsRunning(false)
    }
  }

  // 清空日志
  const clearLogs = () => {
    setLogs([])
    setTestFiles([])
    setProgress({
      phase: 'ready',
      scannedFiles: 0,
      totalFiles: 0,
      downloadedFiles: 0,
      currentFile: '',
      percentage: 0
    })
  }

  // 测试环境检查
  const testEnvironment = () => {
    addLog('🔍 开始环境检查')
    addLog(`📋 fsList函数类型: ${typeof fsList}`)
    addLog(`📋 getLinkByDirAndObj函数类型: ${typeof getLinkByDirAndObj}`)
    addLog(`📋 当前URL: ${window.location.href}`)
    addLog(`📋 模拟选中对象数量: ${mockSelectedObjs.length}`)
    mockSelectedObjs.forEach((obj, index) => {
      addLog(`  ${index + 1}. ${obj.name} (${obj.is_dir ? '文件夹' : '文件'}, ${obj.size} bytes)`)
    })
  }

  onMount(() => {
    addLog('🔧 测试页面初始化完成')
    testEnvironment()
  })

  return (
    <Box p="$6" maxW="1200px" mx="auto">
      <VStack spacing="$6" align="stretch">
        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$4" align="stretch">
            <Heading size="lg">📦 文件夹打包下载测试页面</Heading>

            <Text>
              这是一个专门用于测试文件夹打包下载功能的页面，可以输出详细的调试信息。
            </Text>

            <HStack spacing="$3">
              <Button
                colorScheme="primary"
                onClick={scanFiles}
                disabled={isRunning()}
                loading={isRunning()}
              >
                🚀 开始扫描测试
              </Button>
              <Button
                colorScheme="neutral"
                variant="outline"
                onClick={clearLogs}
              >
                🗑️ 清空日志
              </Button>
              <Button
                colorScheme="accent"
                variant="outline"
                onClick={testEnvironment}
              >
                🔍 环境检查
              </Button>
            </HStack>
          </VStack>
        </Box>

        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$4" align="stretch">
            <Heading size="md">📊 进度状态</Heading>

            <VStack spacing="$3" align="stretch">
              <HStack spacing="$3">
                <Text fontWeight="bold">阶段:</Text>
                <Badge
                  colorScheme={
                    progress().phase === 'complete' ? 'success' :
                    progress().phase === 'error' ? 'danger' :
                    progress().phase === 'ready' ? 'neutral' : 'primary'
                  }
                >
                  {progress().phase}
                </Badge>
              </HStack>

              <Show when={progress().totalFiles > 0}>
                <Progress
                  value={progress().percentage}
                  size="lg"
                  colorScheme="primary"
                />
                <Text fontSize="sm">
                  {progress().scannedFiles}/{progress().totalFiles} 个对象已扫描
                </Text>
              </Show>

              <Show when={progress().currentFile}>
                <Text fontSize="sm" color="$neutral10">
                  当前: {progress().currentFile}
                </Text>
              </Show>
            </VStack>
          </VStack>
        </Box>

        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$4" align="stretch">
            <Heading size="md">📄 扫描结果</Heading>

            <Show
              when={testFiles().length > 0}
              fallback={<Text color="$neutral8">暂无扫描结果</Text>}
            >
              <VStack spacing="$2" align="stretch">
                {testFiles().map((file, index) => (
                  <Box key={index} p="$2" bg="$neutral2" borderRadius="$md">
                    <Text fontWeight="bold">{file.name}</Text>
                    <Text fontSize="sm" color="$neutral10">路径: {file.path}</Text>
                    <Text fontSize="sm" color="$neutral10">大小: {file.size} bytes</Text>
                    <Text
                      fontSize="xs"
                      fontFamily="monospace"
                      wordBreak="break-all"
                      bg="$blackAlpha3"
                      p="$1"
                      borderRadius="$sm"
                    >
                      {file.url}
                    </Text>
                  </Box>
                ))}
              </VStack>
            </Show>
          </VStack>
        </Box>

        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$4" align="stretch">
            <Heading size="md">📝 调试日志</Heading>

            <Box
              maxH="400px"
              overflowY="auto"
              p="$3"
              bg="$blackAlpha3"
              borderRadius="$md"
              fontFamily="monospace"
              fontSize="sm"
            >
              <Show
                when={logs().length > 0}
                fallback={<Text color="$neutral8">暂无日志</Text>}
              >
                <VStack spacing="$1" align="stretch">
                  {logs().map((log, index) => (
                    <Text key={index} whiteSpace="pre-wrap">
                      {log}
                    </Text>
                  ))}
                </VStack>
              </Show>
            </Box>
          </VStack>
        </Box>
      </VStack>
    </Box>
  )
}
