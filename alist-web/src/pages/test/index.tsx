import { Box, VStack, HS<PERSON><PERSON>, Button, <PERSON>ing, Card, CardBody } from "@hope-ui/solid"
import { Route, Routes, useRouter, useLocation } from "@solidjs/router"
import { Show } from "solid-js"
import Upload from "~/pages/home/<USER>/Upload"
import PackageDownloadTest from "./PackageDownloadTest"

const TestHome = () => {
  const { to } = useRouter()

  return (
    <VStack justifyContent="center" h="100vh" spacing="$6" p="$6">
      <Heading size="xl">🧪 AList 测试页面</Heading>

      <HStack spacing="$4">
        <Card>
          <CardBody>
            <VStack spacing="$3">
              <Heading size="md">📦 打包下载测试</Heading>
              <Button
                colorScheme="primary"
                onClick={() => to("/@test/package-download")}
              >
                进入测试页面
              </Button>
            </VStack>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <VStack spacing="$3">
              <Heading size="md">📤 上传测试</Heading>
              <Box w="$md">
                <Upload />
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </HStack>
    </VStack>
  )
}

const Index = () => {
  return (
    <Routes>
      <Route path="/package-download" component={PackageDownloadTest} />
      <Route path="/" component={TestHome} />
    </Routes>
  )
}

export default Index
