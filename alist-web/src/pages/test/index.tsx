import { Box, VStack, HStack, But<PERSON>, Heading } from "@hope-ui/solid"
import { Route, Routes, useRouter, useLocation } from "@solidjs/router"
import { Show } from "solid-js"
import Upload from "~/pages/home/<USER>/Upload"
import PackageDownloadTest from "./PackageDownloadTest"

const TestHome = () => {
  const { to } = useRouter()

  return (
    <VStack justifyContent="center" h="100vh" spacing="$6" p="$6">
      <Heading size="xl">🧪 AList 测试页面</Heading>

      <HStack spacing="$4">
        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$3">
            <Heading size="md">📦 打包下载测试</Heading>
            <Button
              colorScheme="primary"
              onClick={() => to("/@test/package-download")}
            >
              进入测试页面
            </Button>
          </VStack>
        </Box>

        <Box
          p="$6"
          border="1px solid"
          borderColor="$neutral6"
          borderRadius="$md"
          bg="$loContrast"
          shadow="$sm"
        >
          <VStack spacing="$3">
            <Heading size="md">📤 上传测试</Heading>
            <Box w="$md">
              <Upload />
            </Box>
          </VStack>
        </Box>
      </HStack>
    </VStack>
  )
}

const Index = () => {
  return (
    <Routes>
      <Route path="/package-download" component={PackageDownloadTest} />
      <Route path="/" component={TestHome} />
    </Routes>
  )
}

export default Index
