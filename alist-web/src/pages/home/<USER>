import { SideMenuItemProps } from "~/pages/manage/SideMenu"
import { BsFolder, BsShare, BsHddNetwork } from "solid-icons/bs"
import { OcWorkflow2 } from "solid-icons/oc"
import { UserRole } from "~/types"

export const home_side_menu_items: SideMenuItemProps[] = [
  {
    title: "home.sidemenu.my_files",
    icon: BsFolder,
    to: "/",
    role: UserRole.GUEST,
  },
  {
    title: "home.sidemenu.shared_files",
    icon: BsShare,
    to: "/shared",
    role: UserRole.GUEST,
  },

  {
    title: "home.sidemenu.background_tasks",
    icon: OcWorkflow2,
    to: "/@manage/tasks",
    role: UserRole.GENERAL,
  },
]
