import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbProps,
  BreadcrumbSeparator,
  useColorModeValue,
  HStack,
  Icon,
  Box,
} from "@hope-ui/solid"
import { Link } from "@solidjs/router"
import { createMemo, For, Show } from "solid-js"
import { usePath, useRouter, useT } from "~/hooks"
import { getSetting, local, me } from "~/store"
import { UserMethods } from "~/types"
import { encodePath, hoverColor, joinBase } from "~/utils"
import { IoHome } from "solid-icons/io"
import { BsFolder, BsShare } from "solid-icons/bs"
import { OcWorkflow2 } from "solid-icons/oc"

export const Nav = () => {
  const { pathname } = useRouter()
  const t = useT()
  const { setPathAs } = usePath()

  // Process paths to handle virtual storage spaces
  const paths = createMemo(() => {
    const rawPaths = ["", ...pathname().split("/").filter(Boolean)]
    const user = me()

    // Check if this is a virtual storage space path
    if (
      rawPaths.length >= 4 &&
      rawPaths[1] === "virtual" &&
      rawPaths[2] === "storage"
    ) {
      const storageIndex = parseInt(rawPaths[3]) - 1
      const storageName = UserMethods.get_virtual_storage_name(
        user,
        storageIndex,
      )
      const remainingPaths = rawPaths.slice(4)

      // Return: ["", storageName, ...remainingPaths]
      return ["", storageName, ...remainingPaths]
    }

    return rawPaths
  })

  // 根据当前路径确定侧边栏选中状态
  const getCurrentSidebarState = () => {
    const currentPath = pathname()
    if (currentPath.startsWith("/shared")) {
      return {
        title: t("home.sidemenu.shared_files"),
        icon: BsShare,
      }
    } else if (currentPath.startsWith("/@manage/tasks")) {
      return {
        title: t("home.sidemenu.background_tasks"),
        icon: OcWorkflow2,
      }
    } else {
      // 默认为'我的文件'
      return {
        title: t("home.sidemenu.my_files"),
        icon: BsFolder,
      }
    }
  }

  // 检查是否显示侧边栏，动态调整左边距
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  const stickyProps = createMemo<BreadcrumbProps>(() => {
    const mask: BreadcrumbProps = {
      _after: {
        content: "",
        bgColor: "$background",
        position: "absolute",
        height: "100%",
        width: "99vw",
        zIndex: -1,
        transform: "translateX(-50%)",
        left: "50%",
        top: 0,
      },
    }

    switch (local["position_of_header_navbar"]) {
      case "only_navbar_sticky":
        return { ...mask, position: "sticky", zIndex: "$sticky", top: 0 }
      case "sticky":
        return { ...mask, position: "sticky", zIndex: "$sticky", top: 60 }
      default:
        return {
          _after: undefined,
          position: undefined,
          zIndex: undefined,
          top: undefined,
        }
    }
  })

  return (
    <Breadcrumb
      position="fixed"
      top="64px"
      left={{ "@initial": "0", "@md": showSidebar() ? "240px" : "0" }}
      right="0"
      zIndex="$sticky"
      bgColor={useColorModeValue("$neutral1", "#1e2a4a")()}
      borderBottom="1px solid"
      borderColor={useColorModeValue("$neutral4", "#1e3a8a")()}
      h="60px"
      display="flex"
      alignItems="center"
      px="$4"
      pl="20px"
      class="nav"
    >
      <For each={paths()}>
        {(name, i) => {
          const isLast = createMemo(() => i() === paths().length - 1)

          // Build the correct path for navigation
          const path = createMemo(() => {
            const user = me()
            const rawPaths = ["", ...pathname().split("/").filter(Boolean)]

            // Check if we're in a virtual storage space
            if (
              rawPaths.length >= 4 &&
              rawPaths[1] === "virtual" &&
              rawPaths[2] === "storage"
            ) {
              const storageIndex = parseInt(rawPaths[3]) - 1
              const storageName = UserMethods.get_virtual_storage_name(
                user,
                storageIndex,
              )

              if (i() === 0) {
                // Home path
                return ""
              } else if (i() === 1 && name === storageName) {
                // Storage space root
                return `/virtual/storage/${storageIndex + 1}`
              } else if (i() > 1) {
                // Sub-paths within storage space
                const subPaths = paths().slice(2, i() + 1)
                return `/virtual/storage/${storageIndex + 1}/${subPaths.join(
                  "/",
                )}`
              }
            }

            // Normal path handling
            return paths()
              .slice(0, i() + 1)
              .join("/")
          })

          const href = encodePath(path())
          let text = () => name
          const isHome = text() === ""
          if (isHome) {
            const sidebarState = getCurrentSidebarState()
            text = () => getSetting("home_icon") + sidebarState.title
          }
          return (
            <BreadcrumbItem class="nav-item">
              <BreadcrumbLink
                class="nav-link"
                css={{
                  wordBreak: "break-all",
                }}
                color={isHome ? "#3b82f6" : isLast() ? "#ffffff" : "unset"}
                fontSize="$sm"
                fontWeight={isLast() ? "bold" : "normal"}
                _hover={{ bgColor: hoverColor(), color: "unset" }}
                _active={{ transform: "scale(.95)", transition: "0.1s" }}
                cursor="pointer"
                p="$1"
                rounded="$lg"
                currentPage={isLast()}
                as={isLast() ? undefined : Link}
                href={joinBase(href)}
                onMouseEnter={() => setPathAs(path())}
              >
                <Show when={isHome} fallback={text}>
                  <HStack spacing="$1" alignItems="center">
                    <Box
                      w="$6"
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Icon
                        as={getCurrentSidebarState().icon}
                        color="#3b82f6"
                        boxSize="$4"
                      />
                    </Box>
                    <span style={{ color: "#3b82f6", "font-weight": "bold" }}>
                      {text()}
                    </span>
                  </HStack>
                </Show>
              </BreadcrumbLink>
              <Show when={!isLast()}>
                <BreadcrumbSeparator class="nav-separator" color="#9ca3af">
                  {">"}
                </BreadcrumbSeparator>
              </Show>
            </BreadcrumbItem>
          )
        }}
      </For>
    </Breadcrumb>
  )
}
