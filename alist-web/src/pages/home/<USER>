import {
  Box,
  HStack,
  Icon,
  IconButton,
  Menu,
  MenuContent,
  MenuItem,
  MenuTrigger,
  Tooltip,
  useColorModeValue,
} from "@hope-ui/solid"
import { createMemo, Show } from "solid-js"
import { useT, useDownload, usePath } from "~/hooks"
import {
  allChecked,
  checkboxOpen,
  isIndeterminate,
  layout,
  local,
  objStore,
  selectAll,
  setLayout,
  sortObjs,
  State,
  toggleCheckbox,
  userCan,
  haveSelected,
  getSettingBool,
  me,
  oneChecked,
  selectedObjs,
} from "~/store"
import { bus, hoverColor } from "~/utils"
import { ItemCheckbox } from "./folder/helper"
import { operations } from "./toolbar/operations"
import { UserMethods } from "~/types"
import { isArchive } from "~/store/archive"

// 图标导入
import { FaSolidListUl } from "solid-icons/fa"
import { BsGridFill, BsSortDown, BsSearch } from "solid-icons/bs"
import { AiOutlineCloudUpload, AiOutlineCloudDownload } from "solid-icons/ai"
import { CgFolderAdd, CgRename } from "solid-icons/cg"
import { TbCopy, TbFileArrowRight, TbArchive } from "solid-icons/tb"
import { AiTwotoneDelete } from "solid-icons/ai"
import { RiSystemRefreshLine } from "solid-icons/ri"
import { BiRegularRename } from "solid-icons/bi"

export const ToolNavbar = () => {
  const t = useT()
  const { refresh } = usePath()
  const showSidebar = createMemo(() => {
    return local["show_home_sidebar"] !== "none"
  })

  const isFolder = createMemo(() => objStore.state === State.Folder)

  // 工具按钮组件
  const ToolButton = (props: {
    icon: any
    tooltip: string
    onClick: () => void
    disabled?: boolean
    iconSize?: string
    iconColor?: string
  }) => (
    <Tooltip label={t(`home.toolbar.${props.tooltip}`)} placement="bottom">
      <IconButton
        aria-label={props.tooltip}
        icon={
          <Icon
            as={props.icon}
            boxSize={props.iconSize || "$5"}
            color={props.iconColor || (props.disabled ? "#4b5563" : "#9ca3af")}
          />
        }
        size="md"
        variant="ghost"
        color={props.disabled ? "#4b5563" : "#9ca3af"}
        border={`1px solid ${props.disabled ? "#374151" : "#1e3a8a"}`}
        rounded="$md"
        disabled={props.disabled}
        transition="all 0.2s ease"
        _hover={
          props.disabled
            ? {}
            : {
                bgColor: "#1e3a8a",
                transform: "scale(1.08)",
                color: "#ffffff",
              }
        }
        onClick={props.onClick}
      />
    </Tooltip>
  )

  // 带文字和边框的按钮组件
  const TextButton = (props: {
    icon: any
    text: string
    tooltip: string
    onClick: () => void
    disabled?: boolean
    bgColor?: string
    iconColor?: string
    fontWeight?: string
  }) => (
    <Tooltip label={t(`home.toolbar.${props.tooltip}`)} placement="bottom">
      <HStack
        spacing="$2"
        px="$3"
        py="$2"
        border="1px solid #1e3a8a"
        rounded="$md"
        cursor="pointer"
        color="#ffffff"
        bgColor={props.bgColor || "transparent"}
        transition="all 0.2s ease"
        _hover={{
          bgColor: "#1e3a8a",
          transform: "scale(1.08)",
        }}
        onClick={props.onClick}
      >
        <Icon
          as={props.icon}
          boxSize="$5"
          color={props.iconColor || "#ffffff"}
        />
        <span style={{ "font-weight": props.fontWeight || "normal" }}>
          {props.text}
        </span>
      </HStack>
    </Tooltip>
  )

  // 下载菜单组件
  const DownloadMenu = () => {
    const { batchDownloadSelected, sendToAria2, playlistDownloadSelected } =
      useDownload()
    const colorScheme = "neutral"

    return (
      <Menu placement="bottom" offset={10}>
        <Tooltip label={t("home.toolbar.download")} placement="bottom">
          <MenuTrigger
            as={IconButton}
            aria-label="download"
            icon={
              <Icon
                as={AiOutlineCloudDownload}
                boxSize="$6"
                color={haveSelected() ? "#3b82f6" : "#1e3a8a"}
              />
            }
            size="md"
            variant="ghost"
            color={haveSelected() ? "#9ca3af" : "#1e3a8a"}
            border={`1px solid ${haveSelected() ? "#1e3a8a" : "#374151"}`}
            rounded="$md"
            disabled={!haveSelected()}
            transition="all 0.2s ease"
            _hover={
              haveSelected()
                ? {
                    bgColor: "#1e3a8a",
                    transform: "scale(1.08)",
                    color: "#ffffff",
                  }
                : {}
            }
          />
        </Tooltip>
        <MenuContent>
          <MenuItem colorScheme={colorScheme} onSelect={batchDownloadSelected}>
            {t("home.toolbar.batch_download")}
          </MenuItem>
          <Show
            when={
              UserMethods.is_admin(me()) || getSettingBool("package_download")
            }
          >
            <MenuItem
              colorScheme={colorScheme}
              onSelect={() => {
                bus.emit("tool", "package_download")
              }}
            >
              {t("home.toolbar.package_download")}
            </MenuItem>
            <MenuItem
              colorScheme={colorScheme}
              onSelect={playlistDownloadSelected}
            >
              {t("home.toolbar.playlist_download")}
            </MenuItem>
          </Show>
          <MenuItem colorScheme={colorScheme} onSelect={sendToAria2}>
            {t("home.toolbar.send_aria2")}
          </MenuItem>
        </MenuContent>
      </Menu>
    )
  }

  // 视图切换按钮
  const ViewToggleButton = () => (
    <Tooltip
      label={
        layout() === "list" ? t("home.layouts.grid") : t("home.layouts.list")
      }
      placement="bottom"
    >
      <IconButton
        aria-label="toggle-view"
        icon={
          <Icon
            as={layout() === "list" ? BsGridFill : FaSolidListUl}
            boxSize="$5"
          />
        }
        size="md"
        variant="ghost"
        color="#9ca3af"
        border="1px solid #1e3a8a"
        rounded="$md"
        transition="all 0.2s ease"
        _hover={{
          bgColor: "#1e3a8a",
          transform: "scale(1.08)",
          color: "#ffffff",
        }}
        onClick={() => setLayout(layout() === "list" ? "grid" : "list")}
      />
    </Tooltip>
  )

  return (
    <Box
      position="fixed"
      top="124px" // 路径导航栏下方 (64px + 60px)
      left={{ "@initial": "0", "@md": showSidebar() ? "240px" : "0" }}
      right="0"
      zIndex="$sticky"
      bgColor={useColorModeValue("$neutral1", "#1e2a4a")()}
      borderBottom="1px solid"
      borderColor={useColorModeValue("$neutral4", "#1e3a8a")()}
      h="74px"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      px="$4"
      transition="left 0.3s ease"
      class="tool-navbar"
    >
      {/* 左侧工具 */}
      <HStack spacing="$2" ml="8px">
        {/* 全选复选框 */}
        <Show when={isFolder()}>
          <Tooltip label={t("home.toolbar.select_all")} placement="bottom">
            <Box
              w="$10"
              h="$10"
              cursor="pointer"
              bgColor="transparent"
              display="flex"
              justifyContent="center"
              alignItems="center"
              transition="all 0.2s ease"
              mr="-$3"
              _hover={{
                transform: "scale(1.08)",
              }}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                if (!checkboxOpen()) {
                  toggleCheckbox()
                  // 如果复选框刚被打开，等待下一个tick再执行全选
                  setTimeout(() => selectAll(true), 10)
                } else {
                  // 直接切换全选状态
                  const currentlyAllChecked =
                    objStore.objs.length > 0 &&
                    objStore.objs.every((obj) => obj.selected)
                  selectAll(!currentlyAllChecked)
                }
              }}
            >
              <ItemCheckbox
                checked={(() => {
                  if (!checkboxOpen()) return false
                  return (
                    objStore.objs.length > 0 &&
                    objStore.objs.every((obj) => obj.selected)
                  )
                })()}
                indeterminate={(() => {
                  if (!checkboxOpen()) return false
                  const selectedCount = objStore.objs.filter(
                    (obj) => obj.selected,
                  ).length
                  return (
                    selectedCount > 0 && selectedCount < objStore.objs.length
                  )
                })()}
                onChange={(e: any) => {
                  e.stopPropagation()
                  selectAll(e.target.checked as boolean)
                }}
              />
            </Box>
          </Tooltip>
        </Show>

        {/* 上传 */}
        <Show when={isFolder() && (userCan("write") || objStore.write)}>
          <TextButton
            icon={AiOutlineCloudUpload}
            text={t("home.toolbar.upload")}
            tooltip="upload"
            bgColor="#3b82f6"
            fontWeight="bold"
            onClick={() => bus.emit("tool", "upload")}
          />
        </Show>

        {/* 新建文件夹 */}
        <Show when={isFolder() && (userCan("write") || objStore.write)}>
          <TextButton
            icon={CgFolderAdd}
            text={t("home.toolbar.mkdir")}
            tooltip="mkdir"
            fontWeight="bold"
            onClick={() => bus.emit("tool", "mkdir")}
          />
        </Show>

        {/* 刷新 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={RiSystemRefreshLine}
            tooltip="refresh"
            iconSize="$6"
            iconColor="#22c55e"
            onClick={() => refresh(undefined, true)}
          />
        </Show>

        {/* 批量重命名 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={BiRegularRename}
            tooltip="batch_rename"
            iconSize="$6"
            iconColor={haveSelected() ? "#a855f7" : "#581c87"}
            disabled={!haveSelected()}
            onClick={() => {
              bus.emit("tool", "batchRename")
            }}
          />
        </Show>

        {/* 复制按钮已隐藏 */}

        {/* 移动 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={TbFileArrowRight}
            tooltip="move"
            iconSize="$6"
            iconColor={haveSelected() ? "#eab308" : "#78350f"}
            disabled={!haveSelected()}
            onClick={() => bus.emit("tool", "move")}
          />
        </Show>

        {/* 下载 */}
        <Show when={isFolder()}>
          <DownloadMenu />
        </Show>

        {/* 删除 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={AiTwotoneDelete}
            tooltip="delete"
            iconSize="$6"
            iconColor={haveSelected() ? "#ef4444" : "#450a0a"}
            disabled={!haveSelected()}
            onClick={() => bus.emit("tool", "delete")}
          />
        </Show>

        {/* 解压 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={TbArchive}
            tooltip="decompress"
            iconSize="$6"
            iconColor={
              haveSelected() &&
              selectedObjs().every((obj) => !obj.is_dir && isArchive(obj.name))
                ? "#f97316"
                : "#7c2d12"
            }
            disabled={
              !haveSelected() ||
              !selectedObjs().every((obj) => !obj.is_dir && isArchive(obj.name))
            }
            onClick={() => bus.emit("tool", "decompress")}
          />
        </Show>

        {/* 选中文件统计 */}
        <Show when={isFolder() && haveSelected()}>
          <Box
            fontSize="$sm"
            px="$3"
            py="$2"
            rounded="$md"
            whiteSpace="nowrap"
            display="flex"
            alignItems="center"
            fontWeight="bold"
          >
            <Box as="span" color="#ffffff" mx="$1">
              {t("home.toolbar.selected_count", {
                folders: (() => {
                  const selected = selectedObjs()
                  return selected.filter((obj) => obj.is_dir).length
                })(),
                files: (() => {
                  const selected = selectedObjs()
                  return selected.filter((obj) => !obj.is_dir).length
                })(),
              })}
            </Box>
          </Box>
        </Show>
      </HStack>

      {/* 右侧工具 */}
      <HStack spacing="$2">
        {/* 搜索 */}
        <Show when={isFolder()}>
          <ToolButton
            icon={BsSearch}
            tooltip="search"
            onClick={() => bus.emit("tool", "search")}
          />
        </Show>

        {/* 视图切换 */}
        <Show when={isFolder()}>
          <ViewToggleButton />
        </Show>

        {/* 排序菜单 */}
        <Show when={isFolder()}>
          <Menu placement="bottom-end">
            <MenuTrigger
              as={IconButton}
              aria-label="sort"
              icon={<Icon as={BsSortDown} boxSize="$6" />}
              size="md"
              variant="ghost"
              color="#9ca3af"
              border="1px solid #1e3a8a"
              rounded="$md"
              transition="all 0.2s ease"
              _hover={{
                bgColor: "#1e3a8a",
                transform: "scale(1.08)",
                color: "#ffffff",
              }}
            />
            <MenuContent>
              <MenuItem onSelect={() => sortObjs("name")}>
                {t("home.obj.name")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("size")}>
                {t("home.obj.size")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("modified")}>
                {t("home.obj.modified")}
              </MenuItem>
              <MenuItem onSelect={() => sortObjs("type")}>
                {t("home.obj.type")}
              </MenuItem>
            </MenuContent>
          </Menu>
        </Show>
      </HStack>
    </Box>
  )
}
