import { ZIP } from "~/utils/zip-stream"
import streamSaver from "streamsaver"
import { getLinkByDirAndObj, useRouter, useT } from "~/hooks"
import { fsList, pathBase, pathJoin, notify } from "~/utils"
import { password, selectedObjs as _selectedObjs } from "~/store"
import { createSignal, For, Show, onMount, onCleanup, createEffect } from "solid-js"
import {
  Button,
  Heading,
  ModalBody,
  ModalFooter,
  Text,
  VStack,
  HStack,
  Progress,
  Badge,
  Box,
  Divider,
  Tabs,
  TabList,
  Tab,
  TabPanel,
} from "@hope-ui/solid"
import { Obj } from "~/types"
import {
  DownloadQueueManager,
  PerformanceMonitor,
  ProgressInfo,
  FileInfo,
  FailedFile
} from "~/utils/download-queue"
import { memoryManager } from "~/utils/memory-manager"
import { ProgressIndicator } from "~/components/download/ProgressIndicator"
import { FileList } from "~/components/download/FileList"
import { ControlPanel } from "~/components/download/ControlPanel"
import { SessionRecovery } from "~/components/download/SessionRecovery"
import { PerformanceDashboard } from "~/components/download/PerformanceDashboard"
import { downloadStateManager, DownloadSession } from "~/utils/download-state"
// 延迟导入性能监控模块以避免循环依赖
let performanceMonitor: any = null
let adaptiveOptimizer: any = null
let performanceModulesInitialized = false

const initPerformanceModules = async () => {
  try {
    if (!performanceMonitor) {
      const perfModule = await import("~/utils/performance-monitor")
      performanceMonitor = perfModule.performanceMonitor
    }
    if (!adaptiveOptimizer) {
      const optModule = await import("~/utils/adaptive-optimizer")
      adaptiveOptimizer = optModule.adaptiveOptimizer
    }
    performanceModulesInitialized = true
  } catch (error) {
    console.warn('Failed to initialize performance modules:', error)
    performanceModulesInitialized = false
  }
}

streamSaver.mitm = "/streamer/mitm.html"

const trimSlash = (str: string) => {
  return str.replace(/^\/+|\/+$/g, "")
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond: number): string => {
  return formatBytes(bytesPerSecond) + '/s'
}

const PackageDownload = (props: { onClose: () => void }) => {
  const t = useT()
  const { pathname } = useRouter()
  const selectedObjs = _selectedObjs()

  // 状态管理
  const [progress, setProgress] = createSignal<ProgressInfo>({
    phase: 'preparing', // 初始状态改为准备中，等待用户确认
    scannedFiles: 0,
    totalFiles: 0,
    downloadedFiles: 0,
    currentFile: '',
    downloadedSize: 0,
    totalSize: 0,
    failedFiles: [],
    speed: 0
  })

  const [queueManager, setQueueManager] = createSignal<DownloadQueueManager>()
  const [performanceMonitor] = createSignal(new PerformanceMonitor())
  const [showFailedFiles, setShowFailedFiles] = createSignal(false)
  const [showAdvancedInfo, setShowAdvancedInfo] = createSignal(false)
  const [memoryInfo, setMemoryInfo] = createSignal<any>(null)
  const [errorStats, setErrorStats] = createSignal<any>({})
  const [errorAdvice, setErrorAdvice] = createSignal<string[]>([])

  // 新增状态
  const [isPaused, setIsPaused] = createSignal(false)
  const [completedFiles, setCompletedFiles] = createSignal<FileInfo[]>([])
  const [processingFiles, setProcessingFiles] = createSignal<FileInfo[]>([])
  const [estimatedTime, setEstimatedTime] = createSignal(0)
  const [startTime, setStartTime] = createSignal(0)
  const [showProgressDetails, setShowProgressDetails] = createSignal(true)
  const [concurrency, setConcurrency] = createSignal(2)
  const [enableAutoRetry, setEnableAutoRetry] = createSignal(true)
  const [enableMemoryOptimization, setEnableMemoryOptimization] = createSignal(true)
  const [activeTab, setActiveTab] = createSignal(0)

  // 会话恢复相关状态
  const [showSessionRecovery, setShowSessionRecovery] = createSignal(false)
  const [resumeSession, setResumeSession] = createSignal<DownloadSession | null>(null)

  // 性能监控相关状态
  const [showPerformanceDashboard, setShowPerformanceDashboard] = createSignal(false)
  const [autoOptimizeEnabled, setAutoOptimizeEnabled] = createSignal(true)
  // 初始化队列管理器和会话检查
  onMount(() => {
    console.log('🔧 初始化下载队列管理器...')
    const manager = new DownloadQueueManager()
    manager.setProgressCallback(setProgress)
    setQueueManager(manager)
    console.log('✅ 队列管理器初始化完成')

    // 检查是否有未完成的会话
    const incompleteSession = downloadStateManager.hasIncompleteSession()
    if (incompleteSession) {
      console.log('Found incomplete session:', incompleteSession.id)
      setShowSessionRecovery(true)
    } else {
      // 没有未完成会话，显示准备状态，等待用户确认
      console.log('📋 设置初始状态为ready')
      setProgress(prev => ({ ...prev, phase: 'ready' }))
    }

    // 启动性能监控
    initPerformanceModules().then(() => {
      if (performanceModulesInitialized && performanceMonitor && typeof performanceMonitor.startMonitoring === 'function') {
        performanceMonitor.startMonitoring()
      }
      if (performanceModulesInitialized && adaptiveOptimizer && typeof adaptiveOptimizer.startOptimization === 'function') {
        adaptiveOptimizer.startOptimization()
      }
    }).catch(error => {
      console.warn('Failed to initialize performance modules:', error)
    })

    // 清理函数
    onCleanup(() => {
      manager.destroy()
      if (performanceModulesInitialized && performanceMonitor && typeof performanceMonitor.stopMonitoring === 'function') {
        performanceMonitor.stopMonitoring()
      }
      if (performanceModulesInitialized && adaptiveOptimizer && typeof adaptiveOptimizer.stopOptimization === 'function') {
        adaptiveOptimizer.stopOptimization()
      }
    })
  })

  // 性能监控和状态更新
  createEffect(() => {
    const manager = queueManager()
    if (!manager) return

    const interval = setInterval(() => {
      // 性能检查
      performanceMonitor().checkPerformance(manager)

      // 更新内存信息
      const currentMemInfo = manager.getMemoryInfo()
      setMemoryInfo(currentMemInfo)

      // 更新错误统计
      const currentErrorStats = manager.getErrorStats()
      setErrorStats(currentErrorStats)

      // 更新错误建议
      const currentAdvice = manager.getErrorAdvice()
      setErrorAdvice(currentAdvice)

    }, 3000) // 每3秒更新一次

    onCleanup(() => clearInterval(interval))
  })

  // 开始新下载
  const startNewDownload = async () => {
    console.log('🚀 开始新下载，选中对象:', selectedObjs.length)
    const manager = queueManager()
    console.log('📋 队列管理器状态:', manager ? '已准备' : '未准备')
    if (!manager) {
      console.error('❌ 队列管理器未准备好')
      notify.error(t("home.package_download.manager_not_ready"))
      return
    }

    try {
      console.log('📁 开始扫描文件结构...')
      // 扫描文件结构
      const files = await manager.scanFolderStructure(
        selectedObjs,
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password()
      )
      console.log('✅ 文件扫描完成，找到文件数:', files.length)

      if (files.length === 0) {
        console.warn('⚠️ 没有找到文件')
        notify.warning(t("home.package_download.no_files_found"))
        return
      }

      console.log('📦 开始下载和压缩...')
      // 开始下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('Download failed:', error)
      notify.error(`${t("home.package_download.failed")}: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 恢复会话下载
  const resumeSessionDownload = async (session: DownloadSession) => {
    const manager = queueManager()
    if (!manager) {
      notify.error(t("home.package_download.manager_not_ready"))
      return
    }

    try {
      setResumeSession(session)
      notify.info(`恢复下载会话: ${session.name}`)

      // 恢复会话并扫描文件结构
      const files = await manager.scanFolderStructure(
        selectedObjs,
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password(),
        session // 传入会话进行恢复
      )

      if (files.length === 0) {
        notify.warning(t("home.package_download.no_files_found"))
        return
      }

      // 继续下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('Resume download failed:', error)
      notify.error(`恢复下载失败: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 开始下载流程（兼容旧接口）
  const startDownload = async () => {
    const manager = queueManager()
    if (!manager) {
      notify.error(t("home.package_download.manager_not_ready"))
      return
    }

    try {
      // 扫描文件结构
      const files = await manager.scanFolderStructure(
        selectedObjs,
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password()
      )

      if (files.length === 0) {
        notify.warning(t("home.package_download.no_files_found"))
        return
      }

      // 开始下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('Download failed:', error)
      notify.error(`${t("home.package_download.failed")}: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 下载和压缩文件
  const downloadAndCompress = async (files: FileInfo[]) => {
    try {
      let saveName = pathBase(pathname())
      if (selectedObjs.length === 1) {
        saveName = selectedObjs[0].name
      }
      if (!saveName) {
        saveName = t("manage.sidemenu.home")
      }

      const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)
      let fileStream = streamSaver.createWriteStream(`${saveName}.zip`, {
        size: totalSize,
      })

      setProgress(prev => ({ ...prev, phase: 'compressing' }))

      let fileIndex = 0

      // 检查ZIP是否可用
      if (typeof ZIP !== 'function') {
        throw new Error('ZIP module not loaded properly')
      }

      let readableZipStream = new ZIP({
      pull(ctrl: any) {
        if (fileIndex >= files.length) {
          ctrl.close()
          setProgress(prev => ({ ...prev, phase: 'complete' }))
          notify.success(t("home.package_download.success"))
          return
        }

        const file = files[fileIndex]
        let name = trimSlash(file.path)
        if (selectedObjs.length === 1) {
          name = name.replace(`${saveName}/`, "")
        }

        fileIndex++
        setProgress(prev => ({
          ...prev,
          downloadedFiles: fileIndex,
          currentFile: name
        }))

        return fetch(file.url)
          .then((res) => {
            if (!res.ok) {
              throw new Error(`HTTP ${res.status}: ${res.statusText}`)
            }
            ctrl.enqueue({
              name,
              stream: res.body,
            })
          })
          .catch((error) => {
            console.error(`Failed to download ${name}:`, error)
            // 继续处理下一个文件，不中断整个流程
          })
      },
    })

      if (window.WritableStream && readableZipStream.pipeTo) {
        return readableZipStream
          .pipeTo(fileStream)
          .then(() => {
            setProgress(prev => ({ ...prev, phase: 'complete' }))
            notify.success(t("home.package_download.success"))
          })
          .catch((err: any) => {
            console.error('Zip stream error:', err)
            setProgress(prev => ({ ...prev, phase: 'error' }))
            notify.error(`${t("home.package_download.failed")}: ${err}`)
          })
      }
    } catch (error) {
      console.error('Download and compress error:', error)
      setProgress(prev => ({ ...prev, phase: 'error' }))
      notify.error(`${t("home.package_download.failed")}: ${error}`)
    }
  }

  // 取消下载
  const cancelDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.cancel()
    }
    props.onClose()
  }

  // 暂停下载
  const pauseDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.pause()
      setIsPaused(true)
      notify.info(t("home.package_download.paused"))
    }
  }

  // 恢复下载
  const resumeDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.resume()
      setIsPaused(false)
      notify.info(t("home.package_download.resumed"))
    }
  }

  // 重试失败的文件
  const retryFailedFiles = async () => {
    const manager = queueManager()
    if (!manager) return

    try {
      const retryFiles = await manager.retryFailedFiles(
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password()
      )

      if (retryFiles.length > 0) {
        await downloadAndCompress(retryFiles)
      }
    } catch (error) {
      console.error('Retry failed:', error)
      notify.error(`${t("home.package_download.retry_failed")}: ${error}`)
    }
  }

  // 重试单个文件
  const retrySingleFile = async (failedFile: FailedFile) => {
    const manager = queueManager()
    if (!manager) return

    try {
      notify.info(`正在重试: ${failedFile.file.path}`)
      // 这里添加单个文件重试逻辑
    } catch (error) {
      console.error('Single file retry failed:', error)
      notify.error(`重试失败: ${failedFile.file.path}`)
    }
  }

  // 计算预计剩余时间
  createEffect(() => {
    const currentProgress = progress()
    if (currentProgress.speed > 0 && currentProgress.totalFiles > 0) {
      const remainingFiles = currentProgress.totalFiles - currentProgress.downloadedFiles
      const avgTimePerFile = 1 / (currentProgress.speed / (1024 * 1024)) // 假设平均文件大小1MB
      setEstimatedTime(remainingFiles * avgTimePerFile)
    }
  })

  const currentProgress = progress()
  const progressPercentage = currentProgress.totalFiles > 0
    ? Math.round((currentProgress.downloadedFiles / currentProgress.totalFiles) * 100)
    : 0

  return (
    <>
      {/* 会话恢复模态框 */}
      <SessionRecovery
        isOpen={showSessionRecovery()}
        onClose={() => setShowSessionRecovery(false)}
        onResumeSession={(session) => {
          setShowSessionRecovery(false)
          resumeSessionDownload(session)
        }}
        onStartNew={() => {
          setShowSessionRecovery(false)
          startNewDownload()
        }}
      />

      {/* 性能监控仪表板 */}
      <PerformanceDashboard
        isVisible={showPerformanceDashboard()}
        onClose={() => setShowPerformanceDashboard(false)}
        compact={true}
      />

      <ModalBody>
        <VStack w="$full" spacing="$4">
          {/* 确认开始下载界面 */}
          <Show when={currentProgress.phase === 'ready'}>
            <VStack spacing="$4" w="$full" textAlign="center">
              <Heading size="lg">📦 准备打包下载</Heading>
              <Text fontSize="md" color="$neutral11">
                即将开始扫描和打包选中的文件/文件夹
              </Text>
              <VStack spacing="$2" w="$full">
                <Text fontSize="sm" color="$neutral10">
                  选中项目: {selectedObjs.length} 个
                </Text>
                <For each={selectedObjs.slice(0, 5)}>
                  {(obj) => (
                    <Text fontSize="xs" color="$neutral9" noOfLines={1}>
                      📁 {obj.name}
                    </Text>
                  )}
                </For>
                <Show when={selectedObjs.length > 5}>
                  <Text fontSize="xs" color="$neutral8">
                    ... 还有 {selectedObjs.length - 5} 个项目
                  </Text>
                </Show>
              </VStack>
              <HStack spacing="$3" w="$full" justifyContent="center">
                <Button
                  colorScheme="neutral"
                  variant="outline"
                  onClick={props.onClose}
                  size="lg"
                >
                  取消
                </Button>
                <Button
                  colorScheme="primary"
                  onClick={() => {
                    console.log('🔘 用户点击开始打包按钮')
                    startNewDownload().catch(error => {
                      console.error('❌ startNewDownload 执行失败:', error)
                    })
                  }}
                  size="lg"
                  leftIcon={<Text>🚀</Text>}
                >
                  开始打包
                </Button>
              </HStack>
            </VStack>
          </Show>

          {/* 下载进行中的界面 */}
          <Show when={currentProgress.phase !== 'ready'}>
            {/* 主进度指示器 */}
            <ProgressIndicator
            phase={currentProgress.phase}
            current={currentProgress.downloadedFiles}
            total={currentProgress.totalFiles}
            percentage={progressPercentage}
            speed={currentProgress.speed}
            currentItem={currentProgress.currentFile}
            estimatedTime={estimatedTime()}
            showDetails={showProgressDetails()}
            onToggleDetails={() => setShowProgressDetails(!showProgressDetails())}
          />

          {/* 控制面板 */}
          <ControlPanel
            phase={currentProgress.phase}
            isPaused={isPaused()}
            canPause={['scanning', 'downloading', 'compressing'].includes(currentProgress.phase)}
            canCancel={['scanning', 'downloading', 'compressing'].includes(currentProgress.phase)}
            canRetry={['error', 'cancelled'].includes(currentProgress.phase)}
            canResume={isPaused()}

            concurrency={concurrency()}
            maxConcurrency={10}
            enableAutoRetry={enableAutoRetry()}
            enableMemoryOptimization={enableMemoryOptimization()}

            totalFiles={currentProgress.totalFiles}
            completedFiles={currentProgress.downloadedFiles}
            failedFiles={currentProgress.failedFiles.length}
            speed={currentProgress.speed}
            estimatedTime={estimatedTime()}

            onPause={pauseDownload}
            onResume={resumeDownload}
            onCancel={cancelDownload}
            onRetry={retryFailedFiles}
            onClose={props.onClose}
            onConcurrencyChange={(value) => {
              setConcurrency(value)
              if (adaptiveOptimizer) {
                adaptiveOptimizer.setConcurrency(value)
              }
            }}
            onAutoRetryChange={setEnableAutoRetry}
            onMemoryOptimizationChange={setEnableMemoryOptimization}
          />

          {/* 标签页内容 */}
          <Tabs index={activeTab()} onChange={setActiveTab} w="$full">
            <TabList>
              <Tab>
                失败文件 ({currentProgress.failedFiles.length})
              </Tab>
              <Tab>
                已完成 ({completedFiles().length})
              </Tab>
              <Show when={showAdvancedInfo()}>
                <Tab>
                  系统信息
                </Tab>
              </Show>
            </TabList>

            {/* 失败文件标签页 */}
            <TabPanel>
                <FileList
                  files={currentProgress.failedFiles}
                  title="失败文件"
                  type="failed"
                  showRetry={true}
                  onRetry={retrySingleFile}
                  onRetryAll={retryFailedFiles}
                />
              </TabPanel>

              {/* 已完成文件标签页 */}
              <TabPanel>
                <FileList
                  files={completedFiles().map(file => ({
                    file,
                    error: '',
                    retryCount: 0
                  }))}
                  title="已完成文件"
                  type="completed"
                  showRetry={false}
                />
              </TabPanel>

              {/* 系统信息标签页 */}
              <Show when={showAdvancedInfo()}>
                <TabPanel>
                  <VStack spacing="$3" w="$full">
                    {/* 内存信息 */}
                    <Show when={memoryInfo()}>
                      <Box w="$full" p="$3" bg="$neutral2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2">内存使用情况</Text>
                        <HStack justifyContent="space-between">
                          <Text fontSize="xs">使用率:</Text>
                          <Text fontSize="xs" color={memoryInfo()?.usagePercentage > 0.8 ? "$danger11" : "$success11"}>
                            {(memoryInfo()?.usagePercentage * 100).toFixed(1)}%
                          </Text>
                        </HStack>
                        <HStack justifyContent="space-between">
                          <Text fontSize="xs">已使用:</Text>
                          <Text fontSize="xs">{formatBytes(memoryInfo()?.usedJSHeapSize || 0)}</Text>
                        </HStack>
                      </Box>
                    </Show>

                    {/* 错误统计 */}
                    <Show when={Object.keys(errorStats()).length > 0}>
                      <Box w="$full" p="$3" bg="$neutral2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2">错误统计</Text>
                        <For each={Object.entries(errorStats())}>
                          {([errorType, count]) => (
                            <HStack justifyContent="space-between">
                              <Text fontSize="xs">{errorType}:</Text>
                              <Text fontSize="xs" color="$danger11">{count}</Text>
                            </HStack>
                          )}
                        </For>
                      </Box>
                    </Show>

                    {/* 系统建议 */}
                    <Show when={errorAdvice().length > 0}>
                      <Box w="$full" p="$3" bg="$warning2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2" color="$warning11">系统建议</Text>
                        <For each={errorAdvice()}>
                          {(advice) => (
                            <Text fontSize="xs" color="$warning11">• {advice}</Text>
                          )}
                        </For>
                      </Box>
                    </Show>
                  </VStack>
                </TabPanel>
              </Show>
          </Tabs>

          {/* 高级功能按钮 */}
          <Show when={process.env.NODE_ENV === 'development'}>
            <HStack spacing="$2" w="$full">
              <Button
                size="xs"
                variant="ghost"
                onClick={() => setShowAdvancedInfo(!showAdvancedInfo())}
                flex="1"
              >
                {showAdvancedInfo() ? '隐藏系统信息' : '显示系统信息'}
              </Button>
              <Button
                size="xs"
                variant="ghost"
                colorScheme="info"
                onClick={() => setShowPerformanceDashboard(!showPerformanceDashboard())}
                flex="1"
              >
                📊 性能监控
              </Button>
            </HStack>
          </Show>
          </Show>
        </VStack>
      </ModalBody>

      {/* 简化的底部按钮 */}
      <ModalFooter>
        <Show when={['complete', 'error', 'cancelled'].includes(currentProgress.phase)}>
          <Button colorScheme="info" onClick={props.onClose} w="$full">
            {t("global.close")}
          </Button>
        </Show>
      </ModalFooter>
    </>
  )
}

export default PackageDownload
