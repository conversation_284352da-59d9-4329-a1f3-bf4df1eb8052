import { ZIP } from "~/utils/zip-stream"
import streamSaver from "streamsaver"
import { getLinkByDirAndObj, useRouter, useT } from "~/hooks"
import { fsList, pathBase, pathJoin, notify } from "~/utils"
import { password, selectedObjs as _selectedObjs } from "~/store"
import { createSignal, For, Show, onMount, onCleanup, createEffect } from "solid-js"
import {
  Button,
  Heading,
  ModalBody,
  ModalFooter,
  Text,
  VStack,
  HStack,
  Progress,
  Badge,
  Box,
  Divider,
  Tabs,
  TabList,
  Tab,
  TabPanel,
} from "@hope-ui/solid"
import { Obj } from "~/types"
import {
  DownloadQueueManager,
  PerformanceMonitor,
  ProgressInfo,
  FileInfo,
  FailedFile
} from "~/utils/download-queue"
import { memoryManager } from "~/utils/memory-manager"
import { ProgressIndicator } from "~/components/download/ProgressIndicator"
import { FileList } from "~/components/download/FileList"
import { ControlPanel } from "~/components/download/ControlPanel"
import { SessionRecovery } from "~/components/download/SessionRecovery"
import { PerformanceDashboard } from "~/components/download/PerformanceDashboard"
import { downloadStateManager, DownloadSession } from "~/utils/download-state"
// 延迟导入性能监控模块以避免循环依赖
let performanceMonitor: any = null
let adaptiveOptimizer: any = null
let performanceModulesInitialized = false

const initPerformanceModules = async () => {
  try {
    if (!performanceMonitor) {
      const perfModule = await import("~/utils/performance-monitor")
      performanceMonitor = perfModule.performanceMonitor
    }
    if (!adaptiveOptimizer) {
      const optModule = await import("~/utils/adaptive-optimizer")
      adaptiveOptimizer = optModule.adaptiveOptimizer
    }
    performanceModulesInitialized = true
  } catch (error) {
    console.warn('Failed to initialize performance modules:', error)
    performanceModulesInitialized = false
  }
}

streamSaver.mitm = "/streamer/mitm.html"

const trimSlash = (str: string) => {
  return str.replace(/^\/+|\/+$/g, "")
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond: number): string => {
  return formatBytes(bytesPerSecond) + '/s'
}

const PackageDownload = (props: { onClose: () => void }) => {
  const t = useT()
  const { pathname } = useRouter()
  const selectedObjs = _selectedObjs()

  // 状态管理
  const [progress, setProgress] = createSignal<ProgressInfo>({
    phase: 'preparing', // 初始状态改为准备中，等待用户确认
    scannedFiles: 0,
    totalFiles: 0,
    downloadedFiles: 0,
    currentFile: '',
    downloadedSize: 0,
    totalSize: 0,
    failedFiles: [],
    speed: 0
  })

  const [queueManager, setQueueManager] = createSignal<DownloadQueueManager>()
  const [performanceMonitor] = createSignal(new PerformanceMonitor())
  const [showFailedFiles, setShowFailedFiles] = createSignal(false)
  const [showAdvancedInfo, setShowAdvancedInfo] = createSignal(false)
  const [memoryInfo, setMemoryInfo] = createSignal<any>(null)
  const [errorStats, setErrorStats] = createSignal<any>({})
  const [errorAdvice, setErrorAdvice] = createSignal<string[]>([])

  // 新增状态
  const [isPaused, setIsPaused] = createSignal(false)
  const [completedFiles, setCompletedFiles] = createSignal<FileInfo[]>([])
  const [processingFiles, setProcessingFiles] = createSignal<FileInfo[]>([])
  const [estimatedTime, setEstimatedTime] = createSignal(0)
  const [startTime, setStartTime] = createSignal(0)
  const [showProgressDetails, setShowProgressDetails] = createSignal(true)
  const [concurrency, setConcurrency] = createSignal(2)
  const [enableAutoRetry, setEnableAutoRetry] = createSignal(true)
  const [enableMemoryOptimization, setEnableMemoryOptimization] = createSignal(true)
  const [activeTab, setActiveTab] = createSignal(0)

  // 会话恢复相关状态
  const [showSessionRecovery, setShowSessionRecovery] = createSignal(false)
  const [resumeSession, setResumeSession] = createSignal<DownloadSession | null>(null)

  // 性能监控相关状态
  const [showPerformanceDashboard, setShowPerformanceDashboard] = createSignal(false)
  const [autoOptimizeEnabled, setAutoOptimizeEnabled] = createSignal(true)
  // 初始化队列管理器和会话检查
  onMount(() => {
    console.log('🔧 初始化简化下载功能...')
    try {
      // 暂时不使用复杂的队列管理器，直接设置为ready状态
      console.log('📋 设置初始状态为ready')
      setProgress(prev => ({ ...prev, phase: 'ready' }))
      console.log('✅ 简化下载功能初始化完成')
    } catch (error) {
      console.error('❌ 初始化失败:', error)
      notify.error('下载功能初始化失败，请刷新页面重试')
    }

    // 启动性能监控
    initPerformanceModules().then(() => {
      if (performanceModulesInitialized && performanceMonitor && typeof performanceMonitor.startMonitoring === 'function') {
        performanceMonitor.startMonitoring()
      }
      if (performanceModulesInitialized && adaptiveOptimizer && typeof adaptiveOptimizer.startOptimization === 'function') {
        adaptiveOptimizer.startOptimization()
      }
    }).catch(error => {
      console.warn('Failed to initialize performance modules:', error)
    })

    // 清理函数
    onCleanup(() => {
      manager.destroy()
      if (performanceModulesInitialized && performanceMonitor && typeof performanceMonitor.stopMonitoring === 'function') {
        performanceMonitor.stopMonitoring()
      }
      if (performanceModulesInitialized && adaptiveOptimizer && typeof adaptiveOptimizer.stopOptimization === 'function') {
        adaptiveOptimizer.stopOptimization()
      }
    })
  })

  // 性能监控和状态更新
  createEffect(() => {
    const manager = queueManager()
    if (!manager) return

    const interval = setInterval(() => {
      // 性能检查
      performanceMonitor().checkPerformance(manager)

      // 更新内存信息
      const currentMemInfo = manager.getMemoryInfo()
      setMemoryInfo(currentMemInfo)

      // 更新错误统计
      const currentErrorStats = manager.getErrorStats()
      setErrorStats(currentErrorStats)

      // 更新错误建议
      const currentAdvice = manager.getErrorAdvice()
      setErrorAdvice(currentAdvice)

    }, 3000) // 每3秒更新一次

    onCleanup(() => clearInterval(interval))
  })

  // 开始新下载（简化版本）
  const startNewDownload = async () => {
    console.log('🚀 开始新下载，选中对象:', selectedObjs.length)
    console.log('📋 选中对象详情:', selectedObjs.map(obj => ({ name: obj.name, is_dir: obj.is_dir, size: obj.size })))

    // 基础验证
    if (!selectedObjs || selectedObjs.length === 0) {
      console.error('❌ 没有选中任何文件或文件夹')
      notify.error("请先选择要下载的文件或文件夹")
      return
    }

    try {
      console.log('📁 开始简化扫描流程...')

      // 设置初始扫描状态
      setProgress(prev => ({
        ...prev,
        phase: 'scanning',
        scannedFiles: 0,
        totalFiles: 0,
        downloadedFiles: 0,
        currentFile: '正在扫描文件结构...'
      }))

      // 简化的文件扫描逻辑
      const files: FileInfo[] = []
      let scannedCount = 0

      for (const obj of selectedObjs) {
        console.log(`📁 扫描对象: ${obj.name}`)
        scannedCount++

        // 更新扫描进度
        setProgress(prev => ({
          ...prev,
          scannedFiles: scannedCount,
          currentFile: `正在扫描: ${obj.name}`
        }))

        if (!obj.is_dir) {
          // 文件
          const fileInfo: FileInfo = {
            path: obj.name,
            url: getLinkByDirAndObj(pathname(), obj, 'direct', true),
            size: obj.size,
            obj
          }
          files.push(fileInfo)
          console.log(`✅ 添加文件: ${obj.name}`)
        } else {
          // 文件夹 - 简化处理
          try {
            const folderPath = `${pathname()}/${obj.name}`
            console.log(`📂 扫描文件夹: ${folderPath}`)

            const resp = await fsList(folderPath, password())
            if (resp.code === 200 && resp.data.content) {
              for (const item of resp.data.content) {
                if (!item.is_dir) {
                  const fileInfo: FileInfo = {
                    path: `${obj.name}/${item.name}`,
                    url: getLinkByDirAndObj(folderPath, item, 'direct', true),
                    size: item.size,
                    obj: item
                  }
                  files.push(fileInfo)
                }
              }
              console.log(`✅ 文件夹 ${obj.name} 扫描完成，找到 ${resp.data.content.filter((item: any) => !item.is_dir).length} 个文件`)
            }
          } catch (error) {
            console.warn(`⚠️ 扫描文件夹 ${obj.name} 失败:`, error)
          }
        }
      }

      console.log('✅ 文件扫描完成，总共找到文件数:', files.length)

      if (files.length === 0) {
        console.warn('⚠️ 没有找到文件')
        notify.warning(t("home.package_download.no_files_found"))
        setProgress(prev => ({ ...prev, phase: 'error' }))
        return
      }

      // 更新进度状态为准备下载
      setProgress(prev => ({
        ...prev,
        phase: 'downloading',
        totalFiles: files.length,
        scannedFiles: files.length,
        currentFile: '准备开始下载...'
      }))

      console.log('📦 开始下载和压缩...')
      // 开始下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('❌ 下载过程发生错误:', error)
      console.error('❌ 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace')
      notify.error(`${t("home.package_download.failed")}: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 恢复会话下载
  const resumeSessionDownload = async (session: DownloadSession) => {
    const manager = queueManager()
    if (!manager) {
      notify.error(t("home.package_download.manager_not_ready"))
      return
    }

    try {
      setResumeSession(session)
      notify.info(`恢复下载会话: ${session.name}`)

      // 恢复会话并扫描文件结构
      const files = await manager.scanFolderStructure(
        selectedObjs,
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password(),
        session // 传入会话进行恢复
      )

      if (files.length === 0) {
        notify.warning(t("home.package_download.no_files_found"))
        return
      }

      // 继续下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('Resume download failed:', error)
      notify.error(`恢复下载失败: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 开始下载流程（兼容旧接口）
  const startDownload = async () => {
    const manager = queueManager()
    if (!manager) {
      notify.error(t("home.package_download.manager_not_ready"))
      return
    }

    try {
      // 扫描文件结构
      const files = await manager.scanFolderStructure(
        selectedObjs,
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password()
      )

      if (files.length === 0) {
        notify.warning(t("home.package_download.no_files_found"))
        return
      }

      // 开始下载和压缩
      await downloadAndCompress(files)

    } catch (error) {
      console.error('Download failed:', error)
      notify.error(`${t("home.package_download.failed")}: ${error}`)
      setProgress(prev => ({ ...prev, phase: 'error' }))
    }
  }

  // 下载和压缩文件
  const downloadAndCompress = async (files: FileInfo[]) => {
    try {
      console.log('📦 开始下载和压缩流程，文件数量:', files.length)

      let saveName = pathBase(pathname())
      if (selectedObjs.length === 1) {
        saveName = selectedObjs[0].name
      }
      if (!saveName) {
        saveName = t("manage.sidemenu.home")
      }
      console.log('💾 保存文件名:', `${saveName}.zip`)

      const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0)
      console.log('📊 总文件大小:', formatBytes(totalSize))

      let fileStream = streamSaver.createWriteStream(`${saveName}.zip`, {
        size: totalSize,
      })

      console.log('🗜️ 开始压缩阶段...')
      setProgress(prev => ({
        ...prev,
        phase: 'compressing',
        totalFiles: files.length,
        downloadedFiles: 0,
        currentFile: '准备开始压缩...'
      }))

      let fileIndex = 0

      // 检查ZIP是否可用
      if (typeof ZIP !== 'function') {
        console.error('❌ ZIP模块未正确加载')
        throw new Error('ZIP module not loaded properly')
      }
      console.log('✅ ZIP模块检查通过')

      let readableZipStream = new ZIP({
      pull(ctrl: any) {
        if (fileIndex >= files.length) {
          console.log('✅ 所有文件处理完成，关闭ZIP流')
          ctrl.close()
          setProgress(prev => ({ ...prev, phase: 'complete' }))
          notify.success(t("home.package_download.success"))
          return
        }

        const file = files[fileIndex]
        let name = trimSlash(file.path)
        if (selectedObjs.length === 1) {
          name = name.replace(`${saveName}/`, "")
        }

        console.log(`📄 处理文件 ${fileIndex + 1}/${files.length}: ${name}`)
        fileIndex++

        setProgress(prev => ({
          ...prev,
          downloadedFiles: fileIndex,
          currentFile: name
        }))

        return fetch(file.url)
          .then((res) => {
            if (!res.ok) {
              console.error(`❌ 下载失败 ${name}: HTTP ${res.status}`)
              throw new Error(`HTTP ${res.status}: ${res.statusText}`)
            }
            console.log(`✅ 下载成功 ${name}`)
            ctrl.enqueue({
              name,
              stream: res.body,
            })
          })
          .catch((error) => {
            console.error(`❌ 文件下载失败 ${name}:`, error)
            // 记录失败的文件
            setProgress(prev => ({
              ...prev,
              failedFiles: [...prev.failedFiles, {
                file: file,
                error: error.message,
                retryCount: 0
              }]
            }))
            // 继续处理下一个文件，不中断整个流程
          })
      },
    })

      if (window.WritableStream && readableZipStream.pipeTo) {
        return readableZipStream
          .pipeTo(fileStream)
          .then(() => {
            setProgress(prev => ({ ...prev, phase: 'complete' }))
            notify.success(t("home.package_download.success"))
          })
          .catch((err: any) => {
            console.error('Zip stream error:', err)
            setProgress(prev => ({ ...prev, phase: 'error' }))
            notify.error(`${t("home.package_download.failed")}: ${err}`)
          })
      }
    } catch (error) {
      console.error('Download and compress error:', error)
      setProgress(prev => ({ ...prev, phase: 'error' }))
      notify.error(`${t("home.package_download.failed")}: ${error}`)
    }
  }

  // 取消下载
  const cancelDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.cancel()
    }
    props.onClose()
  }

  // 暂停下载
  const pauseDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.pause()
      setIsPaused(true)
      notify.info(t("home.package_download.paused"))
    }
  }

  // 恢复下载
  const resumeDownload = () => {
    const manager = queueManager()
    if (manager) {
      manager.resume()
      setIsPaused(false)
      notify.info(t("home.package_download.resumed"))
    }
  }

  // 重试失败的文件
  const retryFailedFiles = async () => {
    const manager = queueManager()
    if (!manager) return

    try {
      const retryFiles = await manager.retryFailedFiles(
        fsList,
        getLinkByDirAndObj,
        pathname(),
        password()
      )

      if (retryFiles.length > 0) {
        await downloadAndCompress(retryFiles)
      }
    } catch (error) {
      console.error('Retry failed:', error)
      notify.error(`${t("home.package_download.retry_failed")}: ${error}`)
    }
  }

  // 重试单个文件
  const retrySingleFile = async (failedFile: FailedFile) => {
    const manager = queueManager()
    if (!manager) return

    try {
      notify.info(`正在重试: ${failedFile.file.path}`)
      // 这里添加单个文件重试逻辑
    } catch (error) {
      console.error('Single file retry failed:', error)
      notify.error(`重试失败: ${failedFile.file.path}`)
    }
  }

  // 计算预计剩余时间
  createEffect(() => {
    const currentProgress = progress()
    if (currentProgress.speed > 0 && currentProgress.totalFiles > 0) {
      const remainingFiles = currentProgress.totalFiles - currentProgress.downloadedFiles
      const avgTimePerFile = 1 / (currentProgress.speed / (1024 * 1024)) // 假设平均文件大小1MB
      setEstimatedTime(remainingFiles * avgTimePerFile)
    }
  })

  const currentProgress = progress()
  const progressPercentage = currentProgress.totalFiles > 0
    ? Math.round((currentProgress.downloadedFiles / currentProgress.totalFiles) * 100)
    : 0

  return (
    <>
      {/* 会话恢复模态框 */}
      <SessionRecovery
        isOpen={showSessionRecovery()}
        onClose={() => setShowSessionRecovery(false)}
        onResumeSession={(session) => {
          setShowSessionRecovery(false)
          resumeSessionDownload(session)
        }}
        onStartNew={() => {
          setShowSessionRecovery(false)
          startNewDownload()
        }}
      />

      {/* 性能监控仪表板 */}
      <PerformanceDashboard
        isVisible={showPerformanceDashboard()}
        onClose={() => setShowPerformanceDashboard(false)}
        compact={true}
      />

      <ModalBody>
        <VStack w="$full" spacing="$4">
          {/* 确认开始下载界面 */}
          <Show when={currentProgress.phase === 'ready'}>
            <VStack spacing="$4" w="$full" textAlign="center">
              <Heading size="lg">📦 准备打包下载</Heading>
              <Text fontSize="md" color="$neutral11">
                即将开始扫描和打包选中的文件/文件夹
              </Text>
              <VStack spacing="$2" w="$full">
                <Text fontSize="sm" color="$neutral10">
                  选中项目: {selectedObjs.length} 个
                </Text>
                <For each={selectedObjs.slice(0, 5)}>
                  {(obj) => (
                    <Text fontSize="xs" color="$neutral9" noOfLines={1}>
                      📁 {obj.name}
                    </Text>
                  )}
                </For>
                <Show when={selectedObjs.length > 5}>
                  <Text fontSize="xs" color="$neutral8">
                    ... 还有 {selectedObjs.length - 5} 个项目
                  </Text>
                </Show>
              </VStack>
              <HStack spacing="$3" w="$full" justifyContent="center">
                <Button
                  colorScheme="neutral"
                  variant="outline"
                  onClick={props.onClose}
                  size="lg"
                >
                  取消
                </Button>
                <Button
                  colorScheme="primary"
                  onClick={() => {
                    console.log('🔘 用户点击开始打包按钮')
                    console.log('🔍 当前状态检查:', {
                      selectedObjsCount: selectedObjs.length,
                      currentPhase: progress().phase
                    })

                    // 验证前置条件
                    if (!selectedObjs || selectedObjs.length === 0) {
                      console.error('❌ 没有选中任何文件')
                      notify.error("请先选择要下载的文件或文件夹")
                      return
                    }

                    // 执行下载并处理错误
                    startNewDownload().catch(error => {
                      console.error('❌ startNewDownload 执行失败:', error)
                      console.error('❌ 错误详情:', {
                        message: error.message,
                        stack: error.stack,
                        name: error.name
                      })
                      notify.error(`下载启动失败: ${error.message}`)
                      setProgress(prev => ({ ...prev, phase: 'error' }))
                    })
                  }}
                  size="lg"
                  leftIcon={<Text>🚀</Text>}
                  disabled={!selectedObjs || selectedObjs.length === 0}
                >
                  开始打包
                </Button>
              </HStack>
            </VStack>
          </Show>

          {/* 下载进行中的界面 */}
          <Show when={currentProgress.phase !== 'ready'}>
            {/* 主进度指示器 */}
            <ProgressIndicator
            phase={currentProgress.phase}
            current={currentProgress.downloadedFiles}
            total={currentProgress.totalFiles}
            percentage={progressPercentage}
            speed={currentProgress.speed}
            currentItem={currentProgress.currentFile}
            estimatedTime={estimatedTime()}
            showDetails={showProgressDetails()}
            onToggleDetails={() => setShowProgressDetails(!showProgressDetails())}
          />

          {/* 控制面板 */}
          <ControlPanel
            phase={currentProgress.phase}
            isPaused={isPaused()}
            canPause={['scanning', 'downloading', 'compressing'].includes(currentProgress.phase)}
            canCancel={['scanning', 'downloading', 'compressing'].includes(currentProgress.phase)}
            canRetry={['error', 'cancelled'].includes(currentProgress.phase)}
            canResume={isPaused()}

            concurrency={concurrency()}
            maxConcurrency={10}
            enableAutoRetry={enableAutoRetry()}
            enableMemoryOptimization={enableMemoryOptimization()}

            totalFiles={currentProgress.totalFiles}
            completedFiles={currentProgress.downloadedFiles}
            failedFiles={currentProgress.failedFiles.length}
            speed={currentProgress.speed}
            estimatedTime={estimatedTime()}

            onPause={pauseDownload}
            onResume={resumeDownload}
            onCancel={cancelDownload}
            onRetry={retryFailedFiles}
            onClose={props.onClose}
            onConcurrencyChange={(value) => {
              setConcurrency(value)
              if (adaptiveOptimizer) {
                adaptiveOptimizer.setConcurrency(value)
              }
            }}
            onAutoRetryChange={setEnableAutoRetry}
            onMemoryOptimizationChange={setEnableMemoryOptimization}
          />

          {/* 标签页内容 */}
          <Tabs index={activeTab()} onChange={setActiveTab} w="$full">
            <TabList>
              <Tab>
                失败文件 ({currentProgress.failedFiles.length})
              </Tab>
              <Tab>
                已完成 ({completedFiles().length})
              </Tab>
              <Show when={showAdvancedInfo()}>
                <Tab>
                  系统信息
                </Tab>
              </Show>
            </TabList>

            {/* 失败文件标签页 */}
            <TabPanel>
                <FileList
                  files={currentProgress.failedFiles}
                  title="失败文件"
                  type="failed"
                  showRetry={true}
                  onRetry={retrySingleFile}
                  onRetryAll={retryFailedFiles}
                />
              </TabPanel>

              {/* 已完成文件标签页 */}
              <TabPanel>
                <FileList
                  files={completedFiles().map(file => ({
                    file,
                    error: '',
                    retryCount: 0
                  }))}
                  title="已完成文件"
                  type="completed"
                  showRetry={false}
                />
              </TabPanel>

              {/* 系统信息标签页 */}
              <Show when={showAdvancedInfo()}>
                <TabPanel>
                  <VStack spacing="$3" w="$full">
                    {/* 内存信息 */}
                    <Show when={memoryInfo()}>
                      <Box w="$full" p="$3" bg="$neutral2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2">内存使用情况</Text>
                        <HStack justifyContent="space-between">
                          <Text fontSize="xs">使用率:</Text>
                          <Text fontSize="xs" color={memoryInfo()?.usagePercentage > 0.8 ? "$danger11" : "$success11"}>
                            {(memoryInfo()?.usagePercentage * 100).toFixed(1)}%
                          </Text>
                        </HStack>
                        <HStack justifyContent="space-between">
                          <Text fontSize="xs">已使用:</Text>
                          <Text fontSize="xs">{formatBytes(memoryInfo()?.usedJSHeapSize || 0)}</Text>
                        </HStack>
                      </Box>
                    </Show>

                    {/* 错误统计 */}
                    <Show when={Object.keys(errorStats()).length > 0}>
                      <Box w="$full" p="$3" bg="$neutral2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2">错误统计</Text>
                        <For each={Object.entries(errorStats())}>
                          {([errorType, count]) => (
                            <HStack justifyContent="space-between">
                              <Text fontSize="xs">{errorType}:</Text>
                              <Text fontSize="xs" color="$danger11">{count}</Text>
                            </HStack>
                          )}
                        </For>
                      </Box>
                    </Show>

                    {/* 系统建议 */}
                    <Show when={errorAdvice().length > 0}>
                      <Box w="$full" p="$3" bg="$warning2" rounded="$md">
                        <Text fontSize="sm" fontWeight="bold" mb="$2" color="$warning11">系统建议</Text>
                        <For each={errorAdvice()}>
                          {(advice) => (
                            <Text fontSize="xs" color="$warning11">• {advice}</Text>
                          )}
                        </For>
                      </Box>
                    </Show>
                  </VStack>
                </TabPanel>
              </Show>
          </Tabs>

          {/* 高级功能按钮 */}
          <Show when={process.env.NODE_ENV === 'development'}>
            <HStack spacing="$2" w="$full">
              <Button
                size="xs"
                variant="ghost"
                onClick={() => setShowAdvancedInfo(!showAdvancedInfo())}
                flex="1"
              >
                {showAdvancedInfo() ? '隐藏系统信息' : '显示系统信息'}
              </Button>
              <Button
                size="xs"
                variant="ghost"
                colorScheme="info"
                onClick={() => setShowPerformanceDashboard(!showPerformanceDashboard())}
                flex="1"
              >
                📊 性能监控
              </Button>
            </HStack>
          </Show>
          </Show>
        </VStack>
      </ModalBody>

      {/* 简化的底部按钮 */}
      <ModalFooter>
        <Show when={['complete', 'error', 'cancelled'].includes(currentProgress.phase)}>
          <Button colorScheme="info" onClick={props.onClose} w="$full">
            {t("global.close")}
          </Button>
        </Show>
      </ModalFooter>
    </>
  )
}

export default PackageDownload
