// 错误处理和重试系统
export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  TIMEOUT_ERROR = 'timeout_error',
  PERMISSION_ERROR = 'permission_error',
  FILE_NOT_FOUND = 'file_not_found',
  SERVER_ERROR = 'server_error',
  MEMORY_ERROR = 'memory_error',
  ABORT_ERROR = 'abort_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export interface ErrorInfo {
  type: ErrorType
  message: string
  code?: number
  details?: any
  timestamp: number
  retryable: boolean
}

export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  retryableErrors: ErrorType[]
}

export class EnhancedError extends Error {
  public readonly type: ErrorType
  public readonly code?: number
  public readonly details?: any
  public readonly timestamp: number
  public readonly retryable: boolean

  constructor(
    type: ErrorType,
    message: string,
    code?: number,
    details?: any,
    retryable = true
  ) {
    super(message)
    this.name = 'EnhancedError'
    this.type = type
    this.code = code
    this.details = details
    this.timestamp = Date.now()
    this.retryable = retryable
  }

  toErrorInfo(): ErrorInfo {
    return {
      type: this.type,
      message: this.message,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp,
      retryable: this.retryable
    }
  }
}

export class ErrorClassifier {
  // 分类错误类型
  static classifyError(error: any): EnhancedError {
    if (error instanceof EnhancedError) {
      return error
    }

    // 网络错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new EnhancedError(
        ErrorType.NETWORK_ERROR,
        'Network connection failed',
        0,
        error
      )
    }

    // 超时错误
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return new EnhancedError(
        ErrorType.TIMEOUT_ERROR,
        'Request timeout',
        408,
        error
      )
    }

    // 中止错误
    if (error.name === 'AbortError' || error.message.includes('abort')) {
      return new EnhancedError(
        ErrorType.ABORT_ERROR,
        'Operation was aborted',
        0,
        error,
        false // 中止错误不可重试
      )
    }

    // HTTP错误
    if (error.status || error.code) {
      const status = error.status || error.code
      
      if (status === 401 || status === 403) {
        return new EnhancedError(
          ErrorType.PERMISSION_ERROR,
          'Permission denied',
          status,
          error,
          false // 权限错误通常不可重试
        )
      }
      
      if (status === 404) {
        return new EnhancedError(
          ErrorType.FILE_NOT_FOUND,
          'File not found',
          status,
          error,
          false // 文件不存在不可重试
        )
      }
      
      if (status >= 500) {
        return new EnhancedError(
          ErrorType.SERVER_ERROR,
          'Server error',
          status,
          error
        )
      }
    }

    // 内存错误
    if (error.message.includes('memory') || error.message.includes('heap')) {
      return new EnhancedError(
        ErrorType.MEMORY_ERROR,
        'Memory allocation failed',
        0,
        error
      )
    }

    // 默认未知错误
    return new EnhancedError(
      ErrorType.UNKNOWN_ERROR,
      error.message || 'Unknown error occurred',
      0,
      error
    )
  }

  // 判断错误是否可重试
  static isRetryable(error: EnhancedError, config: RetryConfig): boolean {
    return error.retryable && config.retryableErrors.includes(error.type)
  }
}

export class RetryManager {
  private config: RetryConfig
  private retryAttempts: Map<string, number> = new Map()

  constructor(config?: Partial<RetryConfig>) {
    this.config = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      retryableErrors: [
        ErrorType.NETWORK_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.SERVER_ERROR,
        ErrorType.MEMORY_ERROR
      ],
      ...config
    }
  }

  // 执行带重试的操作
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationId: string,
    onRetry?: (attempt: number, error: EnhancedError) => void
  ): Promise<T> {
    let lastError: EnhancedError
    const maxAttempts = this.config.maxRetries + 1

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await operation()
        // 成功后清除重试计数
        this.retryAttempts.delete(operationId)
        return result
      } catch (error) {
        lastError = ErrorClassifier.classifyError(error)
        
        // 如果是最后一次尝试或错误不可重试，直接抛出
        if (attempt === maxAttempts || !ErrorClassifier.isRetryable(lastError, this.config)) {
          this.retryAttempts.delete(operationId)
          throw lastError
        }

        // 记录重试次数
        this.retryAttempts.set(operationId, attempt)
        
        // 通知重试
        onRetry?.(attempt, lastError)

        // 计算延迟时间
        const delay = this.calculateDelay(attempt)
        await this.sleep(delay)
      }
    }

    throw lastError!
  }

  // 计算重试延迟
  private calculateDelay(attempt: number): number {
    const delay = this.config.baseDelay * Math.pow(this.config.backoffFactor, attempt - 1)
    return Math.min(delay, this.config.maxDelay)
  }

  // 睡眠函数
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 获取重试次数
  getRetryCount(operationId: string): number {
    return this.retryAttempts.get(operationId) || 0
  }

  // 重置重试计数
  resetRetryCount(operationId: string) {
    this.retryAttempts.delete(operationId)
  }

  // 清除所有重试计数
  clearAllRetries() {
    this.retryAttempts.clear()
  }

  // 更新配置
  updateConfig(config: Partial<RetryConfig>) {
    this.config = { ...this.config, ...config }
  }
}

// 错误统计和分析
export class ErrorAnalyzer {
  private errorHistory: ErrorInfo[] = []
  private maxHistorySize = 100

  // 记录错误
  recordError(error: EnhancedError) {
    this.errorHistory.push(error.toErrorInfo())
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift()
    }
  }

  // 获取错误统计
  getErrorStats(timeWindow = 300000): { [key in ErrorType]?: number } {
    const cutoff = Date.now() - timeWindow
    const recentErrors = this.errorHistory.filter(error => error.timestamp > cutoff)
    
    const stats: { [key in ErrorType]?: number } = {}
    recentErrors.forEach(error => {
      stats[error.type] = (stats[error.type] || 0) + 1
    })
    
    return stats
  }

  // 获取错误率
  getErrorRate(timeWindow = 300000): number {
    const cutoff = Date.now() - timeWindow
    const recentErrors = this.errorHistory.filter(error => error.timestamp > cutoff)
    
    if (recentErrors.length === 0) return 0
    
    // 这里可以根据实际需求计算错误率
    // 简单实现：错误数量 / 时间窗口（分钟）
    const windowMinutes = timeWindow / 60000
    return recentErrors.length / windowMinutes
  }

  // 获取最频繁的错误类型
  getMostFrequentError(timeWindow = 300000): ErrorType | null {
    const stats = this.getErrorStats(timeWindow)
    const entries = Object.entries(stats) as [ErrorType, number][]
    
    if (entries.length === 0) return null
    
    return entries.reduce((max, current) => 
      current[1] > max[1] ? current : max
    )[0]
  }

  // 获取错误建议
  getErrorAdvice(): string[] {
    const advice: string[] = []
    const stats = this.getErrorStats()
    
    if (stats[ErrorType.NETWORK_ERROR] && stats[ErrorType.NETWORK_ERROR] > 5) {
      advice.push('网络连接不稳定，建议检查网络状况')
      advice.push('考虑降低并发下载数量')
    }
    
    if (stats[ErrorType.TIMEOUT_ERROR] && stats[ErrorType.TIMEOUT_ERROR] > 3) {
      advice.push('请求超时频繁，建议增加超时时间')
      advice.push('检查服务器响应速度')
    }
    
    if (stats[ErrorType.MEMORY_ERROR] && stats[ErrorType.MEMORY_ERROR] > 1) {
      advice.push('内存不足，建议关闭其他标签页')
      advice.push('减少单次下载的文件数量')
    }
    
    if (stats[ErrorType.SERVER_ERROR] && stats[ErrorType.SERVER_ERROR] > 2) {
      advice.push('服务器错误频繁，建议稍后重试')
      advice.push('联系管理员检查服务器状态')
    }
    
    return advice
  }

  // 清除错误历史
  clearHistory() {
    this.errorHistory.length = 0
  }

  // 导出错误报告
  exportErrorReport(): string {
    const stats = this.getErrorStats()
    const errorRate = this.getErrorRate()
    const mostFrequent = this.getMostFrequentError()
    
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      errorStats: stats,
      errorRate,
      mostFrequentError: mostFrequent,
      totalErrors: this.errorHistory.length,
      recentErrors: this.errorHistory.slice(-10) // 最近10个错误
    }, null, 2)
  }
}

// 断路器模式实现
export class CircuitBreaker {
  private failures = 0
  private lastFailureTime = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  
  constructor(
    private failureThreshold = 5,
    private timeout = 60000, // 1分钟
    private monitoringPeriod = 10000 // 10秒
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN'
      } else {
        throw new EnhancedError(
          ErrorType.SERVER_ERROR,
          'Circuit breaker is OPEN',
          503,
          null,
          false
        )
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess() {
    this.failures = 0
    this.state = 'CLOSED'
  }

  private onFailure() {
    this.failures++
    this.lastFailureTime = Date.now()
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN'
    }
  }

  getState(): string {
    return this.state
  }

  reset() {
    this.failures = 0
    this.state = 'CLOSED'
    this.lastFailureTime = 0
  }
}
