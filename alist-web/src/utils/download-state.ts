// 下载状态管理和持久化系统
import { FileInfo, FailedFile, ProgressInfo } from './download-queue'

// 下载会话状态
export interface DownloadSession {
  id: string
  name: string
  startTime: number
  lastUpdateTime: number
  status: 'active' | 'paused' | 'completed' | 'failed' | 'cancelled'
  
  // 基本信息
  selectedPaths: string[]
  currentPath: string
  password?: string
  
  // 进度信息
  totalFiles: number
  scannedFiles: number
  completedFiles: number
  failedFiles: number
  downloadedSize: number
  totalSize: number
  
  // 文件列表
  allFiles: FileInfo[]
  completedFilesList: FileInfo[]
  failedFilesList: FailedFile[]
  
  // 配置信息
  concurrency: number
  enableAutoRetry: boolean
  enableMemoryOptimization: boolean
  
  // 错误统计
  errorStats: Record<string, number>
  retryCount: number
  
  // 元数据
  userAgent: string
  version: string
}

// 会话存储键
const SESSION_STORAGE_KEY = 'alist_download_sessions'
const ACTIVE_SESSION_KEY = 'alist_active_session'
const MAX_SESSIONS = 10 // 最多保存10个会话

// 下载状态管理器
export class DownloadStateManager {
  private static instance: DownloadStateManager
  private currentSession: DownloadSession | null = null
  private autoSaveInterval: number | null = null
  private readonly AUTO_SAVE_INTERVAL = 5000 // 5秒自动保存

  private constructor() {
    this.initializeStorage()
    this.startAutoSave()
  }

  static getInstance(): DownloadStateManager {
    if (!DownloadStateManager.instance) {
      DownloadStateManager.instance = new DownloadStateManager()
    }
    return DownloadStateManager.instance
  }

  // 初始化存储
  private initializeStorage() {
    try {
      // 检查localStorage是否可用
      const testKey = 'test_storage'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
    } catch (error) {
      console.warn('localStorage not available, using memory storage')
    }
  }

  // 创建新的下载会话
  createSession(
    selectedPaths: string[],
    currentPath: string,
    password?: string
  ): DownloadSession {
    const sessionId = this.generateSessionId()
    const now = Date.now()
    
    const session: DownloadSession = {
      id: sessionId,
      name: this.generateSessionName(selectedPaths),
      startTime: now,
      lastUpdateTime: now,
      status: 'active',
      
      selectedPaths,
      currentPath,
      password,
      
      totalFiles: 0,
      scannedFiles: 0,
      completedFiles: 0,
      failedFiles: 0,
      downloadedSize: 0,
      totalSize: 0,
      
      allFiles: [],
      completedFilesList: [],
      failedFilesList: [],
      
      concurrency: 2,
      enableAutoRetry: true,
      enableMemoryOptimization: true,
      
      errorStats: {},
      retryCount: 0,
      
      userAgent: navigator.userAgent,
      version: '1.0.0'
    }

    this.currentSession = session
    this.saveSession(session)
    this.setActiveSession(sessionId)
    
    console.log(`Created download session: ${sessionId}`)
    return session
  }

  // 更新当前会话
  updateSession(updates: Partial<DownloadSession>) {
    if (!this.currentSession) {
      console.warn('No active session to update')
      return
    }

    this.currentSession = {
      ...this.currentSession,
      ...updates,
      lastUpdateTime: Date.now()
    }

    this.saveSession(this.currentSession)
  }

  // 更新进度信息
  updateProgress(progress: ProgressInfo) {
    if (!this.currentSession) return

    const updates: Partial<DownloadSession> = {
      totalFiles: progress.totalFiles,
      scannedFiles: progress.scannedFiles,
      completedFiles: progress.downloadedFiles,
      failedFiles: progress.failedFiles.length,
      downloadedSize: progress.downloadedSize,
      totalSize: progress.totalSize,
      failedFilesList: progress.failedFiles,
      status: this.mapPhaseToStatus(progress.phase)
    }

    this.updateSession(updates)
  }

  // 添加完成的文件
  addCompletedFile(file: FileInfo) {
    if (!this.currentSession) return

    this.currentSession.completedFilesList.push(file)
    this.updateSession({
      completedFilesList: this.currentSession.completedFilesList
    })
  }

  // 设置所有文件列表
  setAllFiles(files: FileInfo[]) {
    if (!this.currentSession) return

    this.updateSession({
      allFiles: files,
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + (file.size || 0), 0)
    })
  }

  // 获取当前会话
  getCurrentSession(): DownloadSession | null {
    return this.currentSession
  }

  // 恢复会话
  resumeSession(sessionId: string): DownloadSession | null {
    const session = this.loadSession(sessionId)
    if (!session) {
      console.error(`Session not found: ${sessionId}`)
      return null
    }

    this.currentSession = session
    this.setActiveSession(sessionId)
    
    console.log(`Resumed download session: ${sessionId}`)
    return session
  }

  // 暂停当前会话
  pauseSession() {
    if (!this.currentSession) return

    this.updateSession({ status: 'paused' })
    console.log(`Paused session: ${this.currentSession.id}`)
  }

  // 完成当前会话
  completeSession() {
    if (!this.currentSession) return

    this.updateSession({ status: 'completed' })
    this.clearActiveSession()
    console.log(`Completed session: ${this.currentSession.id}`)
  }

  // 取消当前会话
  cancelSession() {
    if (!this.currentSession) return

    this.updateSession({ status: 'cancelled' })
    this.clearActiveSession()
    console.log(`Cancelled session: ${this.currentSession.id}`)
  }

  // 标记会话失败
  failSession(error?: string) {
    if (!this.currentSession) return

    this.updateSession({ 
      status: 'failed',
      errorStats: { 
        ...this.currentSession.errorStats, 
        session_error: (this.currentSession.errorStats.session_error || 0) + 1 
      }
    })
    console.log(`Failed session: ${this.currentSession.id}`, error)
  }

  // 获取所有会话
  getAllSessions(): DownloadSession[] {
    try {
      const sessionsData = localStorage.getItem(SESSION_STORAGE_KEY)
      if (!sessionsData) return []
      
      const sessions = JSON.parse(sessionsData) as DownloadSession[]
      return sessions.sort((a, b) => b.lastUpdateTime - a.lastUpdateTime)
    } catch (error) {
      console.error('Failed to load sessions:', error)
      return []
    }
  }

  // 删除会话
  deleteSession(sessionId: string) {
    const sessions = this.getAllSessions()
    const filteredSessions = sessions.filter(s => s.id !== sessionId)
    
    try {
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(filteredSessions))
      
      // 如果删除的是当前活动会话，清除活动会话
      const activeSessionId = localStorage.getItem(ACTIVE_SESSION_KEY)
      if (activeSessionId === sessionId) {
        this.clearActiveSession()
        this.currentSession = null
      }
      
      console.log(`Deleted session: ${sessionId}`)
    } catch (error) {
      console.error('Failed to delete session:', error)
    }
  }

  // 清理过期会话
  cleanupExpiredSessions(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
    const sessions = this.getAllSessions()
    const now = Date.now()
    
    const validSessions = sessions.filter(session => {
      const age = now - session.lastUpdateTime
      return age < maxAge || session.status === 'active'
    })

    // 限制会话数量
    const limitedSessions = validSessions.slice(0, MAX_SESSIONS)
    
    try {
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(limitedSessions))
      console.log(`Cleaned up sessions, kept ${limitedSessions.length} of ${sessions.length}`)
    } catch (error) {
      console.error('Failed to cleanup sessions:', error)
    }
  }

  // 检查是否有未完成的会话
  hasIncompleteSession(): DownloadSession | null {
    const activeSessionId = localStorage.getItem(ACTIVE_SESSION_KEY)
    if (!activeSessionId) return null

    const session = this.loadSession(activeSessionId)
    if (!session) return null

    if (['active', 'paused'].includes(session.status)) {
      return session
    }

    return null
  }

  // 获取会话统计
  getSessionStats(sessionId: string) {
    const session = this.loadSession(sessionId)
    if (!session) return null

    const duration = session.lastUpdateTime - session.startTime
    const completionRate = session.totalFiles > 0 ? 
      (session.completedFiles / session.totalFiles) * 100 : 0
    const avgSpeed = duration > 0 ? session.downloadedSize / (duration / 1000) : 0

    return {
      duration,
      completionRate,
      avgSpeed,
      totalErrors: Object.values(session.errorStats).reduce((sum, count) => sum + count, 0)
    }
  }

  // 导出会话数据
  exportSession(sessionId: string): string | null {
    const session = this.loadSession(sessionId)
    if (!session) return null

    const exportData = {
      session,
      stats: this.getSessionStats(sessionId),
      exportTime: new Date().toISOString()
    }

    return JSON.stringify(exportData, null, 2)
  }

  // 私有方法
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateSessionName(selectedPaths: string[]): string {
    if (selectedPaths.length === 1) {
      return selectedPaths[0].split('/').pop() || 'Download'
    }
    return `${selectedPaths.length} items`
  }

  private mapPhaseToStatus(phase: string): DownloadSession['status'] {
    switch (phase) {
      case 'complete': return 'completed'
      case 'error': return 'failed'
      case 'cancelled': return 'cancelled'
      default: return 'active'
    }
  }

  private saveSession(session: DownloadSession) {
    try {
      const sessions = this.getAllSessions()
      const existingIndex = sessions.findIndex(s => s.id === session.id)
      
      if (existingIndex >= 0) {
        sessions[existingIndex] = session
      } else {
        sessions.unshift(session)
      }

      // 限制会话数量
      const limitedSessions = sessions.slice(0, MAX_SESSIONS)
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(limitedSessions))
    } catch (error) {
      console.error('Failed to save session:', error)
    }
  }

  private loadSession(sessionId: string): DownloadSession | null {
    const sessions = this.getAllSessions()
    return sessions.find(s => s.id === sessionId) || null
  }

  private setActiveSession(sessionId: string) {
    try {
      localStorage.setItem(ACTIVE_SESSION_KEY, sessionId)
    } catch (error) {
      console.error('Failed to set active session:', error)
    }
  }

  private clearActiveSession() {
    try {
      localStorage.removeItem(ACTIVE_SESSION_KEY)
    } catch (error) {
      console.error('Failed to clear active session:', error)
    }
  }

  private startAutoSave() {
    this.autoSaveInterval = window.setInterval(() => {
      if (this.currentSession && this.currentSession.status === 'active') {
        this.saveSession(this.currentSession)
      }
    }, this.AUTO_SAVE_INTERVAL)
  }

  // 销毁管理器
  destroy() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
    }
    
    if (this.currentSession) {
      this.saveSession(this.currentSession)
    }
  }
}

// 导出单例实例
export const downloadStateManager = DownloadStateManager.getInstance()
