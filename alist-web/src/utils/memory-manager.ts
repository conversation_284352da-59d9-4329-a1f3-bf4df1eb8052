// 内存管理工具
export interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  usagePercentage: number
}

export interface MemoryThresholds {
  warning: number    // 警告阈值 (默认70%)
  critical: number   // 危险阈值 (默认85%)
  emergency: number  // 紧急阈值 (默认95%)
}

export class MemoryManager {
  private static instance: MemoryManager
  private thresholds: MemoryThresholds
  private callbacks: Map<string, (info: MemoryInfo) => void>
  private monitorInterval: number | null = null
  private lastCleanupTime = 0
  private readonly CLEANUP_INTERVAL = 30000 // 30秒清理一次
  private memoryWarningShown = false // 避免重复显示内存监控警告

  private constructor() {
    this.thresholds = {
      warning: 0.7,   // 70%
      critical: 0.85, // 85%
      emergency: 0.95 // 95%
    }
    this.callbacks = new Map()
  }

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager()
    }
    return MemoryManager.instance
  }

  // 获取当前内存信息
  getMemoryInfo(): MemoryInfo | null {
    const memory = (performance as any).memory
    if (!memory) {
      if (!this.memoryWarningShown) {
        console.warn('Browser does not support memory monitoring')
        this.memoryWarningShown = true
      }
      return null
    }

    const usagePercentage = memory.usedJSHeapSize / memory.jsHeapSizeLimit

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage
    }
  }

  // 设置内存阈值
  setThresholds(thresholds: Partial<MemoryThresholds>) {
    this.thresholds = { ...this.thresholds, ...thresholds }
  }

  // 注册内存监控回调
  onMemoryChange(id: string, callback: (info: MemoryInfo) => void) {
    this.callbacks.set(id, callback)
  }

  // 移除内存监控回调
  offMemoryChange(id: string) {
    this.callbacks.delete(id)
  }

  // 开始内存监控
  startMonitoring(interval = 5000) {
    if (this.monitorInterval) {
      this.stopMonitoring()
    }

    this.monitorInterval = window.setInterval(() => {
      const memInfo = this.getMemoryInfo()
      if (!memInfo) return

      // 触发回调
      this.callbacks.forEach(callback => {
        try {
          callback(memInfo)
        } catch (error) {
          console.error('Memory callback error:', error)
        }
      })

      // 检查是否需要自动清理
      this.checkAutoCleanup(memInfo)

    }, interval)
  }

  // 停止内存监控
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
  }

  // 检查自动清理
  private checkAutoCleanup(memInfo: MemoryInfo) {
    const now = Date.now()
    
    // 如果内存使用超过危险阈值，立即清理
    if (memInfo.usagePercentage > this.thresholds.critical) {
      console.warn('Critical memory usage detected, forcing cleanup')
      this.forceCleanup()
      this.lastCleanupTime = now
      return
    }

    // 定期清理
    if (now - this.lastCleanupTime > this.CLEANUP_INTERVAL) {
      this.performCleanup()
      this.lastCleanupTime = now
    }
  }

  // 执行内存清理
  performCleanup() {
    console.log('Performing memory cleanup...')
    
    // 清理全局变量
    this.cleanupGlobalVariables()
    
    // 清理事件监听器
    this.cleanupEventListeners()
    
    // 清理缓存
    this.cleanupCaches()
    
    // 建议垃圾回收
    this.suggestGarbageCollection()
  }

  // 强制清理（紧急情况）
  forceCleanup() {
    console.warn('Forcing emergency memory cleanup...')
    
    this.performCleanup()
    
    // 清理更多资源
    this.cleanupLargeObjects()
    
    // 强制垃圾回收
    if ((window as any).gc) {
      (window as any).gc()
    }
  }

  // 清理全局变量
  private cleanupGlobalVariables() {
    // 清理可能的内存泄漏
    const globalVars = ['totalSize', 'downloadFiles', 'tempData']
    globalVars.forEach(varName => {
      if ((window as any)[varName]) {
        delete (window as any)[varName]
      }
    })
  }

  // 清理事件监听器
  private cleanupEventListeners() {
    // 移除可能遗留的事件监听器
    const events = ['beforeunload', 'unload', 'pagehide']
    events.forEach(event => {
      // 这里可以添加具体的清理逻辑
    })
  }

  // 清理缓存
  private cleanupCaches() {
    // 清理可能的缓存数据
    try {
      // 清理 localStorage 中的临时数据
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.warn('Failed to cleanup localStorage:', error)
    }
  }

  // 清理大对象
  private cleanupLargeObjects() {
    // 这里可以添加清理大对象的逻辑
    // 例如清理图片缓存、文件缓存等
  }

  // 建议垃圾回收
  private suggestGarbageCollection() {
    // 在开发环境中强制垃圾回收
    if (process.env.NODE_ENV === 'development' && (window as any).gc) {
      (window as any).gc()
    }
  }

  // 获取内存使用建议
  getMemoryAdvice(memInfo: MemoryInfo): string[] {
    const advice: string[] = []
    
    if (memInfo.usagePercentage > this.thresholds.emergency) {
      advice.push('紧急：内存使用过高，建议立即关闭其他标签页')
      advice.push('考虑分批下载文件，减少单次下载数量')
    } else if (memInfo.usagePercentage > this.thresholds.critical) {
      advice.push('警告：内存使用较高，建议减少并发下载数量')
      advice.push('关闭不必要的浏览器标签页')
    } else if (memInfo.usagePercentage > this.thresholds.warning) {
      advice.push('提示：内存使用正常，但建议监控使用情况')
    }

    return advice
  }

  // 格式化内存大小
  formatMemorySize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  }

  // 销毁管理器
  destroy() {
    this.stopMonitoring()
    this.callbacks.clear()
  }
}

// 流式数据处理器
export class StreamProcessor {
  private chunks: Uint8Array[] = []
  private totalSize = 0
  private maxChunkSize: number
  private onChunkProcessed?: (size: number) => void

  constructor(maxChunkSize = 1024 * 1024) { // 默认1MB
    this.maxChunkSize = maxChunkSize
  }

  // 添加数据块
  addChunk(chunk: Uint8Array) {
    this.chunks.push(chunk)
    this.totalSize += chunk.length

    // 如果累积数据过大，立即处理
    if (this.totalSize > this.maxChunkSize) {
      this.processChunks()
    }
  }

  // 处理数据块
  private processChunks() {
    if (this.chunks.length === 0) return

    const processedSize = this.totalSize
    
    // 清理已处理的数据
    this.chunks.length = 0
    this.totalSize = 0

    // 通知处理完成
    this.onChunkProcessed?.(processedSize)
  }

  // 设置处理回调
  setOnChunkProcessed(callback: (size: number) => void) {
    this.onChunkProcessed = callback
  }

  // 强制处理所有剩余数据
  flush() {
    this.processChunks()
  }

  // 获取当前缓冲区大小
  getBufferSize(): number {
    return this.totalSize
  }

  // 清理资源
  destroy() {
    this.chunks.length = 0
    this.totalSize = 0
    this.onChunkProcessed = undefined
  }
}

// 资源池管理器
export class ResourcePool<T> {
  private pool: T[] = []
  private createFn: () => T
  private resetFn?: (item: T) => void
  private destroyFn?: (item: T) => void
  private maxSize: number

  constructor(
    createFn: () => T,
    maxSize = 10,
    resetFn?: (item: T) => void,
    destroyFn?: (item: T) => void
  ) {
    this.createFn = createFn
    this.resetFn = resetFn
    this.destroyFn = destroyFn
    this.maxSize = maxSize
  }

  // 获取资源
  acquire(): T {
    if (this.pool.length > 0) {
      const item = this.pool.pop()!
      this.resetFn?.(item)
      return item
    }
    return this.createFn()
  }

  // 释放资源
  release(item: T) {
    if (this.pool.length < this.maxSize) {
      this.pool.push(item)
    } else {
      this.destroyFn?.(item)
    }
  }

  // 清理资源池
  clear() {
    this.pool.forEach(item => this.destroyFn?.(item))
    this.pool.length = 0
  }

  // 获取池大小
  size(): number {
    return this.pool.length
  }
}

// 导出单例实例
export const memoryManager = MemoryManager.getInstance()
