import PQueue from 'p-queue'
import { Obj } from '~/types'
import {
  memoryManager,
  MemoryInfo,
  StreamProcessor,
  ResourcePool
} from './memory-manager'
import {
  RetryManager,
  ErrorAnalyzer,
  CircuitBreaker,
  EnhancedError,
  ErrorType,
  ErrorClassifier
} from './error-handler'
import {
  downloadStateManager,
  DownloadSession
} from './download-state'
// 延迟导入以避免循环依赖
let performanceMonitor: any = null
let adaptiveOptimizer: any = null

// 延迟初始化性能监控模块
const initPerformanceModules = async () => {
  if (!performanceMonitor) {
    const perfModule = await import('./performance-monitor')
    performanceMonitor = perfModule.performanceMonitor
  }
  if (!adaptiveOptimizer) {
    const optModule = await import('./adaptive-optimizer')
    adaptiveOptimizer = optModule.adaptiveOptimizer
  }
}

// 配置常量
export const DOWNLOAD_CONFIG = {
  BATCH_SIZE: 50,                    // 每批处理文件数量
  CONCURRENT_API_CALLS: 3,           // 并发API调用数
  CONCURRENT_DOWNLOADS: 2,           // 并发下载数
  API_TIMEOUT: 30000,                // API超时时间(ms)
  DOWNLOAD_TIMEOUT: 60000,           // 下载超时时间(ms)
  MAX_RETRIES: 2,                    // 最大重试次数
  RETRY_DELAY: 1000,                 // 重试延迟(ms)
}

// 文件信息接口
export interface FileInfo {
  path: string
  url: string
  size?: number
  obj: Obj
}

// 失败文件信息
export interface FailedFile {
  file: FileInfo
  error: string
  retryCount: number
}

// 进度信息
export interface ProgressInfo {
  phase: 'preparing' | 'ready' | 'scanning' | 'downloading' | 'compressing' | 'complete' | 'error' | 'cancelled'
  scannedFiles: number
  totalFiles: number
  downloadedFiles: number
  currentFile: string
  downloadedSize: number
  totalSize: number
  failedFiles: FailedFile[]
  speed: number // 下载速度 (bytes/s)
}

// 下载队列管理器
export class DownloadQueueManager {
  private apiQueue: PQueue
  private downloadQueue: PQueue
  private abortController: AbortController
  private progressCallback?: (progress: ProgressInfo) => void
  private progress: ProgressInfo
  private startTime: number = 0
  private lastProgressTime: number = 0
  private lastDownloadedSize: number = 0

  // 新增的管理器
  private retryManager: RetryManager
  private errorAnalyzer: ErrorAnalyzer
  private circuitBreaker: CircuitBreaker
  private streamProcessor: StreamProcessor
  private memoryMonitorId: string

  // 资源池
  private requestPool: ResourcePool<XMLHttpRequest>
  private streamPool: ResourcePool<StreamProcessor>

  // 会话管理
  private currentSession: DownloadSession | null = null
  private sessionId: string | null = null

  // 性能监控
  private performanceMonitorId: string
  private optimizationCallbackId: string
  private lastSpeedUpdate: number = 0
  private speedSamples: number[] = []

  constructor() {
    this.apiQueue = new PQueue({
      concurrency: DOWNLOAD_CONFIG.CONCURRENT_API_CALLS,
      timeout: DOWNLOAD_CONFIG.API_TIMEOUT,
      throwOnTimeout: true
    })

    this.downloadQueue = new PQueue({
      concurrency: DOWNLOAD_CONFIG.CONCURRENT_DOWNLOADS,
      timeout: DOWNLOAD_CONFIG.DOWNLOAD_TIMEOUT,
      throwOnTimeout: true
    })

    this.abortController = new AbortController()
    this.progress = this.createInitialProgress()

    // 初始化新的管理器
    this.retryManager = new RetryManager({
      maxRetries: DOWNLOAD_CONFIG.MAX_RETRIES,
      baseDelay: DOWNLOAD_CONFIG.RETRY_DELAY,
      maxDelay: 30000,
      backoffFactor: 2
    })

    this.errorAnalyzer = new ErrorAnalyzer()
    this.circuitBreaker = new CircuitBreaker(5, 60000, 10000)
    this.streamProcessor = new StreamProcessor(1024 * 1024) // 1MB chunks

    // 生成唯一的内存监控ID
    this.memoryMonitorId = `download_queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 生成性能监控ID
    this.performanceMonitorId = `download_queue_perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.optimizationCallbackId = `download_queue_opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 初始化资源池
    this.requestPool = new ResourcePool(
      () => new XMLHttpRequest(),
      5, // 最多5个连接
      (xhr) => xhr.abort(), // 重置时中止请求
      (xhr) => xhr.abort()  // 销毁时中止请求
    )

    this.streamPool = new ResourcePool(
      () => new StreamProcessor(),
      3, // 最多3个流处理器
      (processor) => processor.flush(), // 重置时清空缓冲区
      (processor) => processor.destroy() // 销毁时清理资源
    )

    // 设置监控
    this.setupMemoryMonitoring()
    this.setupPerformanceMonitoring()
  }

  private createInitialProgress(): ProgressInfo {
    return {
      phase: 'scanning',
      scannedFiles: 0,
      totalFiles: 0,
      downloadedFiles: 0,
      currentFile: '',
      downloadedSize: 0,
      totalSize: 0,
      failedFiles: [],
      speed: 0
    }
  }

  // 设置内存监控
  private setupMemoryMonitoring() {
    memoryManager.onMemoryChange(this.memoryMonitorId, (memInfo: MemoryInfo) => {
      // 根据内存使用情况调整策略
      if (memInfo.usagePercentage > 0.85) {
        console.warn('High memory usage detected, reducing concurrency')
        this.adjustConcurrency('reduce')

        // 强制清理
        this.performMemoryCleanup()
      } else if (memInfo.usagePercentage > 0.7) {
        console.log('Moderate memory usage, maintaining current settings')
      } else if (memInfo.usagePercentage < 0.4 && this.apiQueue.concurrency < 6) {
        console.log('Low memory usage, can increase concurrency')
        this.adjustConcurrency('increase')
      }
    })

    // 开始监控
    memoryManager.startMonitoring(3000) // 每3秒检查一次
  }

  // 执行内存清理
  private performMemoryCleanup() {
    try {
      // 清理流处理器
      this.streamProcessor.flush()

      // 清理资源池
      this.streamPool.clear()

      // 清理已完成的任务
      this.apiQueue.clear()
      this.downloadQueue.clear()

      // 建议垃圾回收
      memoryManager.performCleanup()

      console.log('Memory cleanup completed')
    } catch (error) {
      console.error('Memory cleanup failed:', error)
    }
  }

  // 设置性能监控
  private async setupPerformanceMonitoring() {
    try {
      // 延迟初始化性能监控模块
      await initPerformanceModules()

      // 监听性能指标更新
      if (performanceMonitor) {
        performanceMonitor.onMetricsUpdate(this.performanceMonitorId, (metrics: any) => {
          this.updatePerformanceMetrics(metrics)
        })
      }

      // 监听优化决策
      if (adaptiveOptimizer) {
        adaptiveOptimizer.onOptimization(this.optimizationCallbackId, (decision: any) => {
          this.handleOptimizationDecision(decision)
        })
      }

      console.log('Performance monitoring setup completed')
    } catch (error) {
      console.warn('Performance monitoring setup failed:', error)
    }
  }

  // 更新性能指标
  private updatePerformanceMetrics(metrics: any) {
    if (!performanceMonitor) return

    try {
      // 更新处理指标
      performanceMonitor.updateProcessingMetrics(
        this.calculateProcessingSpeed(),
        this.apiQueue.size + this.downloadQueue.size,
        this.getActiveConnections()
      )

      // 更新错误指标
      const errorStats = this.errorAnalyzer.getErrorStats()
      const totalErrors = Object.values(errorStats).reduce((sum, count) => sum + count, 0)
      const errorRate = totalErrors > 0 ? totalErrors / (this.progress.totalFiles || 1) : 0
      const successRate = this.progress.totalFiles > 0 ?
        (this.progress.downloadedFiles / this.progress.totalFiles) : 1

      performanceMonitor.updateErrorMetrics(errorRate, 0, successRate)
    } catch (error) {
      console.warn('Failed to update performance metrics:', error)
    }
  }

  // 处理优化决策
  private handleOptimizationDecision(decision: any) {
    try {
      if (decision.action === 'emergency') {
        console.warn('Emergency optimization triggered:', decision.reason)
        this.performMemoryCleanup()
      }

      // 应用并发数调整
      if (decision.newConcurrency !== this.apiQueue.concurrency) {
        this.adjustConcurrencyTo(decision.newConcurrency)
        console.log(`Concurrency auto-adjusted to ${decision.newConcurrency}: ${decision.reason}`)
      }
    } catch (error) {
      console.warn('Failed to handle optimization decision:', error)
    }
  }

  // 调整并发数到指定值
  private adjustConcurrencyTo(newConcurrency: number) {
    const oldConcurrency = this.apiQueue.concurrency

    // 更新API队列并发数
    this.apiQueue.concurrency = newConcurrency
    this.downloadQueue.concurrency = Math.min(newConcurrency, DOWNLOAD_CONFIG.CONCURRENT_DOWNLOADS)

    // 更新配置
    DOWNLOAD_CONFIG.CONCURRENT_API_CALLS = newConcurrency

    console.log(`Concurrency adjusted: ${oldConcurrency} → ${newConcurrency}`)
  }

  // 计算处理速度
  private calculateProcessingSpeed(): number {
    const now = Date.now()
    if (this.lastSpeedUpdate === 0) {
      this.lastSpeedUpdate = now
      return 0
    }

    const timeDiff = (now - this.lastSpeedUpdate) / 1000 // 秒
    if (timeDiff < 1) return 0 // 至少1秒间隔

    const filesProcessed = this.progress.downloadedFiles
    const speed = filesProcessed / (timeDiff / 60) // 文件/分钟转换为文件/秒

    // 保存速度样本
    this.speedSamples.push(speed)
    if (this.speedSamples.length > 10) {
      this.speedSamples.shift()
    }

    // 返回平均速度
    return this.speedSamples.reduce((sum, s) => sum + s, 0) / this.speedSamples.length
  }

  // 获取活动连接数
  private getActiveConnections(): number {
    return this.apiQueue.pending + this.downloadQueue.pending
  }

  // 设置进度回调
  setProgressCallback(callback: (progress: ProgressInfo) => void) {
    this.progressCallback = callback
  }

  // 更新进度（增强版，同步到会话）
  private updateProgress(updates: Partial<ProgressInfo>) {
    console.log('📊 DownloadQueueManager.updateProgress 被调用:', updates)

    const oldProgress = { ...this.progress }
    this.progress = { ...this.progress, ...updates }

    // 计算下载速度
    const now = Date.now()
    if (this.lastProgressTime > 0) {
      const timeDiff = (now - this.lastProgressTime) / 1000 // 秒
      const sizeDiff = this.progress.downloadedSize - this.lastDownloadedSize
      if (timeDiff > 0) {
        this.progress.speed = sizeDiff / timeDiff
      }
    }

    this.lastProgressTime = now
    this.lastDownloadedSize = this.progress.downloadedSize

    console.log('📈 进度更新详情:', {
      phase: `${oldProgress.phase} → ${this.progress.phase}`,
      files: `${oldProgress.downloadedFiles}/${oldProgress.totalFiles} → ${this.progress.downloadedFiles}/${this.progress.totalFiles}`,
      currentFile: this.progress.currentFile,
      hasCallback: !!this.progressCallback
    })

    // 同步到会话
    if (this.currentSession) {
      downloadStateManager.updateProgress(this.progress)
    }

    // 确保回调函数存在并调用
    if (this.progressCallback) {
      console.log('📞 调用进度回调函数...')
      try {
        this.progressCallback(this.progress)
        console.log('✅ 进度回调函数调用成功')
      } catch (error) {
        console.error('❌ 进度回调函数调用失败:', error)
      }
    } else {
      console.warn('⚠️ 进度回调函数不存在')
    }
  }

  // 添加完成的文件到会话
  addCompletedFile(file: FileInfo) {
    if (this.currentSession) {
      downloadStateManager.addCompletedFile(file)
    }
  }

  // 创建新的下载会话
  createDownloadSession(
    selectedObjs: Obj[],
    currentPath: string,
    password?: string
  ): DownloadSession {
    const selectedPaths = selectedObjs.map(obj => obj.name)
    const session = downloadStateManager.createSession(selectedPaths, currentPath, password)

    this.currentSession = session
    this.sessionId = session.id

    console.log(`Created download session: ${session.id}`)
    return session
  }

  // 恢复现有会话
  resumeDownloadSession(session: DownloadSession): boolean {
    try {
      this.currentSession = session
      this.sessionId = session.id

      // 恢复进度状态
      this.progress = {
        phase: session.status === 'completed' ? 'complete' :
               session.status === 'failed' ? 'error' :
               session.status === 'cancelled' ? 'cancelled' : 'scanning',
        scannedFiles: session.scannedFiles,
        totalFiles: session.totalFiles,
        downloadedFiles: session.completedFiles,
        currentFile: '',
        downloadedSize: session.downloadedSize,
        totalSize: session.totalSize,
        failedFiles: session.failedFilesList,
        speed: 0
      }

      // 恢复配置
      DOWNLOAD_CONFIG.CONCURRENT_API_CALLS = session.concurrency
      DOWNLOAD_CONFIG.CONCURRENT_DOWNLOADS = session.concurrency

      console.log(`Resumed download session: ${session.id}`)
      return true
    } catch (error) {
      console.error('Failed to resume session:', error)
      return false
    }
  }

  // 分批获取文件结构（增强版，支持会话）
  async scanFolderStructure(
    selectedObjs: Obj[],
    fsList: (path: string, password?: string) => Promise<any>,
    getLinkByDirAndObj: (dir: string, obj: Obj, type?: string, encodeAll?: boolean) => string,
    currentPath: string,
    password?: string,
    resumeSession?: DownloadSession
  ): Promise<FileInfo[]> {
    this.startTime = Date.now()

    // 如果是恢复会话，使用现有数据
    if (resumeSession) {
      if (this.resumeDownloadSession(resumeSession)) {
        // 如果会话已经扫描完成，直接返回文件列表
        if (resumeSession.allFiles.length > 0) {
          this.updateProgress({
            phase: 'downloading',
            totalFiles: resumeSession.allFiles.length,
            totalSize: resumeSession.totalSize
          })
          return resumeSession.allFiles
        }
      }
    } else {
      // 创建新会话
      this.createDownloadSession(selectedObjs, currentPath, password)
    }

    console.log('🔍 开始扫描阶段，选中对象数量:', selectedObjs.length)
    console.log('📋 选中对象列表:', selectedObjs.map(obj => ({ name: obj.name, is_dir: obj.is_dir, size: obj.size })))

    this.updateProgress({
      phase: 'scanning',
      scannedFiles: 0,
      totalFiles: 0,
      downloadedFiles: 0,
      currentFile: '开始扫描文件结构...'
    })

    const allFiles: FileInfo[] = []

    try {
      for (let i = 0; i < selectedObjs.length; i++) {
        const obj = selectedObjs[i]
        console.log(`📁 扫描对象 ${i + 1}/${selectedObjs.length}:`, obj.name, '类型:', obj.is_dir ? '文件夹' : '文件')

        if (this.abortController.signal.aborted) {
          throw new Error('Operation cancelled')
        }

        // 更新当前扫描状态
        this.updateProgress({
          currentFile: `正在扫描: ${obj.name}`,
          scannedFiles: i
        })

        const files = await this.scanSingleObject(
          obj,
          '',
          fsList,
          getLinkByDirAndObj,
          currentPath,
          password
        )

        console.log(`✅ 对象 ${obj.name} 扫描完成，找到 ${files.length} 个文件`)
        allFiles.push(...files)

        // 更新扫描进度
        this.updateProgress({
          scannedFiles: i + 1,
          totalFiles: allFiles.length,
          currentFile: `已扫描: ${obj.name} (${files.length} 个文件)`
        })
      }

      // 保存扫描结果到会话
      if (this.currentSession) {
        downloadStateManager.setAllFiles(allFiles)
      }

      this.updateProgress({
        phase: 'downloading',
        totalFiles: allFiles.length,
        totalSize: allFiles.reduce((sum, file) => sum + (file.size || 0), 0)
      })

      return allFiles
    } catch (error) {
      this.updateProgress({ phase: 'error' })
      if (this.currentSession) {
        downloadStateManager.failSession(error instanceof Error ? error.message : String(error))
      }
      throw error
    }
  }

  // 递归扫描单个对象（增强错误处理）
  private async scanSingleObject(
    obj: Obj,
    prefix: string,
    fsList: (path: string, password?: string) => Promise<any>,
    getLinkByDirAndObj: (dir: string, obj: Obj, type?: string, encodeAll?: boolean) => string,
    currentPath: string,
    password?: string
  ): Promise<FileInfo[]> {
    if (this.abortController.signal.aborted) {
      throw new EnhancedError(ErrorType.ABORT_ERROR, 'Operation cancelled', 0, null, false)
    }

    if (!obj.is_dir) {
      // 文件
      const fileInfo: FileInfo = {
        path: prefix ? `${prefix}/${obj.name}` : obj.name,
        url: getLinkByDirAndObj(
          prefix ? `${currentPath}/${prefix}` : currentPath,
          obj,
          'direct',
          true
        ),
        size: obj.size,
        obj
      }

      this.updateProgress({
        scannedFiles: this.progress.scannedFiles + 1,
        currentFile: fileInfo.path
      })

      return [fileInfo]
    } else {
      // 文件夹 - 使用重试机制和断路器
      const folderPath = prefix ? `${currentPath}/${prefix}/${obj.name}` : `${currentPath}/${obj.name}`
      const operationId = `scan_${folderPath}`

      try {
        const resp = await this.retryManager.executeWithRetry(
          async () => {
            return await this.circuitBreaker.execute(async () => {
              return await this.apiQueue.add(
                () => fsList(folderPath, password),
                { signal: this.abortController.signal }
              )
            })
          },
          operationId,
          (attempt, error) => {
            console.warn(`Retry attempt ${attempt} for ${folderPath}:`, error.message)
            this.errorAnalyzer.recordError(error)
          }
        )

        if (resp.code !== 200) {
          throw new EnhancedError(
            ErrorType.SERVER_ERROR,
            resp.message || 'Failed to list folder',
            resp.code
          )
        }

        const files: FileInfo[] = []
        const newPrefix = prefix ? `${prefix}/${obj.name}` : obj.name

        // 分批处理子项目
        const items = resp.data.content || []
        for (let i = 0; i < items.length; i += DOWNLOAD_CONFIG.BATCH_SIZE) {
          if (this.abortController.signal.aborted) {
            throw new EnhancedError(ErrorType.ABORT_ERROR, 'Operation cancelled', 0, null, false)
          }

          const batch = items.slice(i, i + DOWNLOAD_CONFIG.BATCH_SIZE)
          const batchPromises = batch.map((item: Obj) =>
            this.scanSingleObject(item, newPrefix, fsList, getLinkByDirAndObj, currentPath, password)
          )

          const batchResults = await Promise.allSettled(batchPromises)

          for (const result of batchResults) {
            if (result.status === 'fulfilled') {
              files.push(...result.value)
            } else {
              const error = ErrorClassifier.classifyError(result.reason)
              this.errorAnalyzer.recordError(error)
              console.warn('Failed to scan item:', error.message)

              // 将失败的项目添加到失败列表
              this.progress.failedFiles.push({
                file: {
                  path: newPrefix,
                  url: '',
                  obj: item,
                  size: item.size
                },
                error: error.message,
                retryCount: 0
              })
            }
          }
        }

        return files
      } catch (error) {
        const enhancedError = ErrorClassifier.classifyError(error)
        this.errorAnalyzer.recordError(enhancedError)
        console.error(`Failed to scan folder ${folderPath}:`, enhancedError.message)
        throw enhancedError
      }
    }
  }

  // 重试失败的文件（增强版）
  async retryFailedFiles(
    fsList: (path: string, password?: string) => Promise<any>,
    getLinkByDirAndObj: (dir: string, obj: Obj, type?: string, encodeAll?: boolean) => string,
    currentPath: string,
    password?: string
  ): Promise<FileInfo[]> {
    const retryFiles: FileInfo[] = []
    const stillFailedFiles: FailedFile[] = []

    console.log(`Retrying ${this.progress.failedFiles.length} failed files...`)

    for (const failedFile of this.progress.failedFiles) {
      if (failedFile.retryCount >= DOWNLOAD_CONFIG.MAX_RETRIES) {
        stillFailedFiles.push(failedFile)
        continue
      }

      const operationId = `retry_${failedFile.file.path}_${failedFile.retryCount}`

      try {
        if (this.abortController.signal.aborted) {
          break
        }

        // 使用重试管理器重新扫描失败的文件
        const files = await this.retryManager.executeWithRetry(
          async () => {
            return await this.scanSingleObject(
              failedFile.file.obj,
              '',
              fsList,
              getLinkByDirAndObj,
              currentPath,
              password
            )
          },
          operationId,
          (attempt, error) => {
            console.warn(`Retry attempt ${attempt} for ${failedFile.file.path}:`, error.message)
          }
        )

        retryFiles.push(...files)
        console.log(`Successfully retried: ${failedFile.file.path}`)

      } catch (error) {
        const enhancedError = ErrorClassifier.classifyError(error)
        this.errorAnalyzer.recordError(enhancedError)

        stillFailedFiles.push({
          ...failedFile,
          retryCount: failedFile.retryCount + 1,
          error: enhancedError.message
        })

        console.error(`Retry failed for ${failedFile.file.path}:`, enhancedError.message)
      }
    }

    this.updateProgress({ failedFiles: stillFailedFiles })

    // 提供错误分析建议
    if (stillFailedFiles.length > 0) {
      const advice = this.errorAnalyzer.getErrorAdvice()
      if (advice.length > 0) {
        console.warn('Error analysis suggestions:', advice)
      }
    }

    return retryFiles
  }

  // 取消操作（增强版）
  cancel() {
    this.abortController.abort()
    this.apiQueue.clear()
    this.downloadQueue.clear()
    this.updateProgress({ phase: 'cancelled' })

    // 更新会话状态
    if (this.currentSession) {
      downloadStateManager.cancelSession()
    }
  }

  // 暂停下载
  pause() {
    // 暂停队列处理
    this.apiQueue.pause()
    this.downloadQueue.pause()

    // 更新会话状态
    if (this.currentSession) {
      downloadStateManager.pauseSession()
    }

    console.log('Download paused')
  }

  // 恢复下载
  resume() {
    // 恢复队列处理
    this.apiQueue.start()
    this.downloadQueue.start()

    // 更新会话状态
    if (this.currentSession) {
      downloadStateManager.updateSession({ status: 'active' })
    }

    console.log('Download resumed')
  }

  // 完成下载
  complete() {
    this.updateProgress({ phase: 'complete' })

    // 更新会话状态
    if (this.currentSession) {
      downloadStateManager.completeSession()
    }

    console.log('Download completed')
  }

  // 清理资源（增强版）
  destroy() {
    console.log('Destroying download queue manager...')

    // 保存当前会话状态
    if (this.currentSession && this.currentSession.status === 'active') {
      downloadStateManager.pauseSession()
    }

    // 取消所有操作
    this.cancel()

    // 清理内存监控
    memoryManager.offMemoryChange(this.memoryMonitorId)

    // 清理性能监控
    try {
      if (performanceMonitor) {
        performanceMonitor.offMetricsUpdate(this.performanceMonitorId)
      }
      if (adaptiveOptimizer) {
        adaptiveOptimizer.offOptimization(this.optimizationCallbackId)
      }
    } catch (error) {
      console.warn('Failed to cleanup performance monitoring:', error)
    }

    // 清理资源池
    this.requestPool.clear()
    this.streamPool.clear()

    // 清理流处理器
    this.streamProcessor.destroy()

    // 清理重试管理器
    this.retryManager.clearAllRetries()

    // 清理错误分析器
    this.errorAnalyzer.clearHistory()

    // 重置断路器
    this.circuitBreaker.reset()

    // 执行最终内存清理
    this.performMemoryCleanup()

    // 清理会话引用
    this.currentSession = null
    this.sessionId = null

    console.log('Download queue manager destroyed')
  }

  // 获取当前会话
  getCurrentSession(): DownloadSession | null {
    return this.currentSession
  }

  // 获取会话ID
  getSessionId(): string | null {
    return this.sessionId
  }

  // 检查是否有活动会话
  hasActiveSession(): boolean {
    return this.currentSession !== null && this.currentSession.status === 'active'
  }

  // 获取错误统计
  getErrorStats() {
    return this.errorAnalyzer.getErrorStats()
  }

  // 获取错误建议
  getErrorAdvice(): string[] {
    return this.errorAnalyzer.getErrorAdvice()
  }

  // 获取断路器状态
  getCircuitBreakerState(): string {
    return this.circuitBreaker.getState()
  }

  // 导出错误报告
  exportErrorReport(): string {
    return this.errorAnalyzer.exportErrorReport()
  }

  // 获取内存信息
  getMemoryInfo() {
    return memoryManager.getMemoryInfo()
  }

  // 获取当前进度
  getProgress(): ProgressInfo {
    return { ...this.progress }
  }

  // 动态调整并发数
  adjustConcurrency(direction: 'increase' | 'reduce') {
    const currentApiConcurrency = this.apiQueue.concurrency
    const currentDownloadConcurrency = this.downloadQueue.concurrency

    if (direction === 'reduce') {
      this.apiQueue.concurrency = Math.max(1, currentApiConcurrency - 1)
      this.downloadQueue.concurrency = Math.max(1, currentDownloadConcurrency - 1)
    } else {
      this.apiQueue.concurrency = Math.min(6, currentApiConcurrency + 1)
      this.downloadQueue.concurrency = Math.min(4, currentDownloadConcurrency + 1)
    }

    console.log(`Adjusted concurrency: API=${this.apiQueue.concurrency}, Download=${this.downloadQueue.concurrency}`)
  }
}

// 性能监控工具（增强版）
export class PerformanceMonitor {
  private lastMemoryCheck = 0
  private readonly CHECK_INTERVAL = 5000 // 5秒检查一次
  private performanceHistory: Array<{
    timestamp: number
    memoryUsage: number
    errorRate: number
    queueSize: number
  }> = []

  checkPerformance(queueManager: DownloadQueueManager): void {
    const now = Date.now()
    if (now - this.lastMemoryCheck < this.CHECK_INTERVAL) {
      return
    }

    this.lastMemoryCheck = now

    // 获取内存信息
    const memInfo = queueManager.getMemoryInfo()
    if (!memInfo) return

    // 获取错误统计
    const errorStats = queueManager.getErrorStats()
    const totalErrors = Object.values(errorStats).reduce((sum, count) => sum + count, 0)
    const errorRate = totalErrors / (this.CHECK_INTERVAL / 1000) // 每秒错误数

    // 记录性能历史
    this.performanceHistory.push({
      timestamp: now,
      memoryUsage: memInfo.usagePercentage,
      errorRate,
      queueSize: queueManager.apiQueue.size + queueManager.downloadQueue.size
    })

    // 限制历史记录大小
    if (this.performanceHistory.length > 100) {
      this.performanceHistory.shift()
    }

    // 性能分析和建议
    this.analyzePerformance(queueManager, memInfo, errorRate)
  }

  private analyzePerformance(
    queueManager: DownloadQueueManager,
    memInfo: any,
    errorRate: number
  ): void {
    // 内存使用分析
    if (memInfo.usagePercentage > 0.9) {
      console.error('Critical memory usage! Forcing emergency cleanup')
      queueManager.adjustConcurrency('reduce')
      memoryManager.forceCleanup()
    } else if (memInfo.usagePercentage > 0.8) {
      console.warn('High memory usage detected, reducing concurrency')
      queueManager.adjustConcurrency('reduce')
    } else if (memInfo.usagePercentage < 0.4 && errorRate < 0.1) {
      console.log('Low memory usage and low error rate, can increase concurrency')
      queueManager.adjustConcurrency('increase')
    }

    // 错误率分析
    if (errorRate > 1) { // 每秒超过1个错误
      console.warn('High error rate detected, reducing concurrency')
      queueManager.adjustConcurrency('reduce')
    }

    // 断路器状态检查
    const circuitState = queueManager.getCircuitBreakerState()
    if (circuitState === 'OPEN') {
      console.warn('Circuit breaker is OPEN, pausing operations')
    }
  }

  // 获取性能报告
  getPerformanceReport(): string {
    if (this.performanceHistory.length === 0) {
      return 'No performance data available'
    }

    const recent = this.performanceHistory.slice(-10)
    const avgMemory = recent.reduce((sum, item) => sum + item.memoryUsage, 0) / recent.length
    const avgErrorRate = recent.reduce((sum, item) => sum + item.errorRate, 0) / recent.length
    const avgQueueSize = recent.reduce((sum, item) => sum + item.queueSize, 0) / recent.length

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      averageMemoryUsage: `${(avgMemory * 100).toFixed(2)}%`,
      averageErrorRate: avgErrorRate.toFixed(2),
      averageQueueSize: avgQueueSize.toFixed(0),
      totalDataPoints: this.performanceHistory.length,
      memoryAdvice: memoryManager.getMemoryAdvice(memoryManager.getMemoryInfo() || {
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0,
        usagePercentage: 0
      })
    }, null, 2)
  }

  // 清理性能历史
  clearHistory(): void {
    this.performanceHistory.length = 0
  }
}
