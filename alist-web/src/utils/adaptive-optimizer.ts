// 自适应优化器 - 动态参数调整系统
import { performanceMonitor, PerformanceMetrics } from './performance-monitor'
import { memoryManager } from './memory-manager'

// 优化配置
export interface OptimizationConfig {
  // 内存优化
  memoryThresholds: {
    reduce: number      // 降低并发的内存阈值
    cleanup: number     // 强制清理的内存阈值
    emergency: number   // 紧急处理的内存阈值
  }
  
  // 网络优化
  networkThresholds: {
    slowSpeed: number      // 慢速网络阈值 (bytes/s)
    highLatency: number    // 高延迟阈值 (ms)
    errorRate: number      // 错误率阈值
  }
  
  // 并发控制
  concurrencyLimits: {
    min: number         // 最小并发数
    max: number         // 最大并发数
    step: number        // 调整步长
  }
  
  // 调整频率
  adjustmentInterval: number  // 调整间隔 (ms)
  
  // 学习参数
  learningRate: number       // 学习率
  adaptationSpeed: number    // 适应速度
}

// 性能状态
export interface PerformanceState {
  currentConcurrency: number
  targetConcurrency: number
  memoryPressure: number
  networkQuality: number
  systemLoad: number
  adaptationScore: number
}

// 优化决策
export interface OptimizationDecision {
  action: 'increase' | 'decrease' | 'maintain' | 'emergency'
  reason: string
  newConcurrency: number
  confidence: number
  expectedImprovement: number
}

// 历史性能数据
interface PerformanceHistory {
  timestamp: number
  concurrency: number
  metrics: PerformanceMetrics
  score: number
}

export class AdaptiveOptimizer {
  private static instance: AdaptiveOptimizer
  private config: OptimizationConfig
  private state: PerformanceState
  private history: PerformanceHistory[] = []
  private optimizationInterval: number | null = null
  private callbacks: Map<string, (decision: OptimizationDecision) => void> = new Map()
  private readonly MAX_HISTORY = 100

  private constructor() {
    this.config = this.createDefaultConfig()
    this.state = this.createInitialState()
    this.startOptimization()
  }

  static getInstance(): AdaptiveOptimizer {
    if (!AdaptiveOptimizer.instance) {
      AdaptiveOptimizer.instance = new AdaptiveOptimizer()
    }
    return AdaptiveOptimizer.instance
  }

  // 创建默认配置
  private createDefaultConfig(): OptimizationConfig {
    return {
      memoryThresholds: {
        reduce: 0.75,     // 75%时开始降低并发
        cleanup: 0.85,    // 85%时强制清理
        emergency: 0.95   // 95%时紧急处理
      },
      networkThresholds: {
        slowSpeed: 100 * 1024,  // 100KB/s
        highLatency: 1000,      // 1000ms
        errorRate: 0.1          // 10%
      },
      concurrencyLimits: {
        min: 1,
        max: 8,
        step: 1
      },
      adjustmentInterval: 10000,  // 10秒
      learningRate: 0.1,
      adaptationSpeed: 0.2
    }
  }

  // 创建初始状态
  private createInitialState(): PerformanceState {
    return {
      currentConcurrency: 2,
      targetConcurrency: 2,
      memoryPressure: 0,
      networkQuality: 1,
      systemLoad: 0,
      adaptationScore: 0.5
    }
  }

  // 开始优化
  startOptimization() {
    if (this.optimizationInterval) return

    this.optimizationInterval = window.setInterval(() => {
      this.performOptimization()
    }, this.config.adjustmentInterval)

    console.log('Adaptive optimization started')
  }

  // 停止优化
  stopOptimization() {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval)
      this.optimizationInterval = null
    }
    console.log('Adaptive optimization stopped')
  }

  // 执行优化
  private performOptimization() {
    const metrics = performanceMonitor.getCurrentMetrics()
    
    // 更新状态
    this.updateState(metrics)
    
    // 记录历史
    this.recordHistory(metrics)
    
    // 做出优化决策
    const decision = this.makeOptimizationDecision(metrics)
    
    // 应用决策
    this.applyDecision(decision)
    
    // 通知回调
    this.notifyCallbacks(decision)
    
    // 学习和适应
    this.learnFromHistory()
  }

  // 更新状态
  private updateState(metrics: PerformanceMetrics) {
    // 计算内存压力
    this.state.memoryPressure = metrics.memoryUsage

    // 计算网络质量
    this.state.networkQuality = this.calculateNetworkQuality(metrics)

    // 计算系统负载
    this.state.systemLoad = this.calculateSystemLoad(metrics)

    // 更新适应分数
    this.state.adaptationScore = this.calculateAdaptationScore(metrics)
  }

  // 计算网络质量
  private calculateNetworkQuality(metrics: PerformanceMetrics): number {
    let quality = 1.0

    // 速度因子
    if (metrics.downloadSpeed < this.config.networkThresholds.slowSpeed) {
      quality *= 0.5
    }

    // 延迟因子
    if (metrics.networkLatency > this.config.networkThresholds.highLatency) {
      quality *= 0.7
    }

    // 错误率因子
    if (metrics.errorRate > this.config.networkThresholds.errorRate) {
      quality *= (1 - metrics.errorRate)
    }

    return Math.max(0.1, quality)
  }

  // 计算系统负载
  private calculateSystemLoad(metrics: PerformanceMetrics): number {
    const memoryLoad = metrics.memoryUsage
    const queueLoad = Math.min(1, metrics.queueSize / 100)
    const errorLoad = metrics.errorRate

    return (memoryLoad * 0.5 + queueLoad * 0.3 + errorLoad * 0.2)
  }

  // 计算适应分数
  private calculateAdaptationScore(metrics: PerformanceMetrics): number {
    const successWeight = 0.4
    const speedWeight = 0.3
    const memoryWeight = 0.2
    const errorWeight = 0.1

    const successScore = metrics.successRate
    const speedScore = Math.min(1, metrics.downloadSpeed / (1024 * 1024)) // 1MB/s为满分
    const memoryScore = 1 - metrics.memoryUsage
    const errorScore = 1 - metrics.errorRate

    return (
      successScore * successWeight +
      speedScore * speedWeight +
      memoryScore * memoryWeight +
      errorScore * errorWeight
    )
  }

  // 记录历史
  private recordHistory(metrics: PerformanceMetrics) {
    const historyEntry: PerformanceHistory = {
      timestamp: Date.now(),
      concurrency: this.state.currentConcurrency,
      metrics: { ...metrics },
      score: this.state.adaptationScore
    }

    this.history.push(historyEntry)

    // 限制历史记录大小
    if (this.history.length > this.MAX_HISTORY) {
      this.history.shift()
    }
  }

  // 做出优化决策
  private makeOptimizationDecision(metrics: PerformanceMetrics): OptimizationDecision {
    // 紧急情况处理
    if (metrics.memoryUsage > this.config.memoryThresholds.emergency) {
      return {
        action: 'emergency',
        reason: '内存使用过高，紧急降低并发数',
        newConcurrency: Math.max(this.config.concurrencyLimits.min, 1),
        confidence: 1.0,
        expectedImprovement: 0.8
      }
    }

    // 内存压力处理
    if (metrics.memoryUsage > this.config.memoryThresholds.reduce) {
      return {
        action: 'decrease',
        reason: '内存压力较高，降低并发数',
        newConcurrency: Math.max(
          this.config.concurrencyLimits.min,
          this.state.currentConcurrency - this.config.concurrencyLimits.step
        ),
        confidence: 0.8,
        expectedImprovement: 0.3
      }
    }

    // 网络质量处理
    if (this.state.networkQuality < 0.5) {
      return {
        action: 'decrease',
        reason: '网络质量较差，降低并发数',
        newConcurrency: Math.max(
          this.config.concurrencyLimits.min,
          this.state.currentConcurrency - this.config.concurrencyLimits.step
        ),
        confidence: 0.7,
        expectedImprovement: 0.4
      }
    }

    // 性能良好，考虑增加并发
    if (this.state.adaptationScore > 0.8 && 
        metrics.memoryUsage < 0.6 && 
        this.state.networkQuality > 0.8) {
      return {
        action: 'increase',
        reason: '系统性能良好，可以增加并发数',
        newConcurrency: Math.min(
          this.config.concurrencyLimits.max,
          this.state.currentConcurrency + this.config.concurrencyLimits.step
        ),
        confidence: 0.6,
        expectedImprovement: 0.2
      }
    }

    // 基于历史数据的智能决策
    const historicalDecision = this.makeHistoricalDecision()
    if (historicalDecision) {
      return historicalDecision
    }

    // 默认保持当前状态
    return {
      action: 'maintain',
      reason: '当前性能稳定，保持现有配置',
      newConcurrency: this.state.currentConcurrency,
      confidence: 0.5,
      expectedImprovement: 0
    }
  }

  // 基于历史数据的决策
  private makeHistoricalDecision(): OptimizationDecision | null {
    if (this.history.length < 5) return null

    const recentHistory = this.history.slice(-5)
    const avgScore = recentHistory.reduce((sum, h) => sum + h.score, 0) / recentHistory.length

    // 如果最近的性能在下降
    if (avgScore < this.state.adaptationScore - 0.1) {
      // 寻找历史上表现最好的并发数
      const bestHistory = this.history
        .filter(h => h.score > avgScore + 0.1)
        .sort((a, b) => b.score - a.score)[0]

      if (bestHistory && bestHistory.concurrency !== this.state.currentConcurrency) {
        return {
          action: bestHistory.concurrency > this.state.currentConcurrency ? 'increase' : 'decrease',
          reason: `基于历史数据，并发数${bestHistory.concurrency}时性能更好`,
          newConcurrency: bestHistory.concurrency,
          confidence: 0.7,
          expectedImprovement: bestHistory.score - avgScore
        }
      }
    }

    return null
  }

  // 应用决策
  private applyDecision(decision: OptimizationDecision) {
    const oldConcurrency = this.state.currentConcurrency
    this.state.currentConcurrency = decision.newConcurrency
    this.state.targetConcurrency = decision.newConcurrency

    // 如果是紧急情况，还需要执行额外的清理
    if (decision.action === 'emergency') {
      memoryManager.forceCleanup()
    }

    if (oldConcurrency !== decision.newConcurrency) {
      console.log(`Concurrency adjusted: ${oldConcurrency} → ${decision.newConcurrency} (${decision.reason})`)
    }
  }

  // 从历史中学习
  private learnFromHistory() {
    if (this.history.length < 10) return

    // 分析不同并发数下的性能表现
    const concurrencyPerformance = new Map<number, number[]>()
    
    this.history.forEach(h => {
      if (!concurrencyPerformance.has(h.concurrency)) {
        concurrencyPerformance.set(h.concurrency, [])
      }
      concurrencyPerformance.get(h.concurrency)!.push(h.score)
    })

    // 找出最优并发数
    let bestConcurrency = this.state.currentConcurrency
    let bestScore = 0

    concurrencyPerformance.forEach((scores, concurrency) => {
      const avgScore = scores.reduce((sum, s) => sum + s, 0) / scores.length
      if (avgScore > bestScore) {
        bestScore = avgScore
        bestConcurrency = concurrency
      }
    })

    // 更新目标并发数
    if (bestConcurrency !== this.state.targetConcurrency) {
      this.state.targetConcurrency = bestConcurrency
    }
  }

  // 通知回调
  private notifyCallbacks(decision: OptimizationDecision) {
    this.callbacks.forEach(callback => {
      try {
        callback(decision)
      } catch (error) {
        console.error('Optimization callback error:', error)
      }
    })
  }

  // 注册回调
  onOptimization(id: string, callback: (decision: OptimizationDecision) => void) {
    this.callbacks.set(id, callback)
  }

  // 移除回调
  offOptimization(id: string) {
    this.callbacks.delete(id)
  }

  // 手动调整并发数
  setConcurrency(concurrency: number) {
    const clampedConcurrency = Math.max(
      this.config.concurrencyLimits.min,
      Math.min(this.config.concurrencyLimits.max, concurrency)
    )
    
    this.state.currentConcurrency = clampedConcurrency
    this.state.targetConcurrency = clampedConcurrency
    
    console.log(`Manual concurrency adjustment: ${clampedConcurrency}`)
  }

  // 获取当前状态
  getState(): PerformanceState {
    return { ...this.state }
  }

  // 获取配置
  getConfig(): OptimizationConfig {
    return { ...this.config }
  }

  // 更新配置
  updateConfig(updates: Partial<OptimizationConfig>) {
    this.config = { ...this.config, ...updates }
  }

  // 获取性能历史
  getPerformanceHistory(): PerformanceHistory[] {
    return [...this.history]
  }

  // 获取优化统计
  getOptimizationStats() {
    if (this.history.length === 0) return null

    const scores = this.history.map(h => h.score)
    const concurrencies = this.history.map(h => h.concurrency)
    
    return {
      avgScore: scores.reduce((sum, s) => sum + s, 0) / scores.length,
      maxScore: Math.max(...scores),
      minScore: Math.min(...scores),
      avgConcurrency: concurrencies.reduce((sum, c) => sum + c, 0) / concurrencies.length,
      optimizationCount: this.history.filter((h, i) => 
        i > 0 && h.concurrency !== this.history[i - 1].concurrency
      ).length
    }
  }

  // 重置优化器
  reset() {
    this.history.length = 0
    this.state = this.createInitialState()
    console.log('Adaptive optimizer reset')
  }

  // 销毁优化器
  destroy() {
    this.stopOptimization()
    this.callbacks.clear()
    this.history.length = 0
  }
}

// 导出单例实例
export const adaptiveOptimizer = AdaptiveOptimizer.getInstance()
