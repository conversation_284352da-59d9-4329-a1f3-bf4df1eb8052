// 性能监控和优化系统
import { memoryManager, MemoryInfo } from './memory-manager'

// 性能指标接口
export interface PerformanceMetrics {
  timestamp: number
  
  // 内存指标
  memoryUsage: number          // 内存使用率 (0-1)
  memoryUsed: number           // 已使用内存 (bytes)
  memoryTotal: number          // 总内存限制 (bytes)
  
  // 网络指标
  downloadSpeed: number        // 下载速度 (bytes/s)
  uploadSpeed: number          // 上传速度 (bytes/s)
  networkLatency: number       // 网络延迟 (ms)
  networkErrors: number        // 网络错误数
  
  // 处理指标
  processingSpeed: number      // 处理速度 (files/s)
  queueSize: number           // 队列大小
  activeConnections: number    // 活动连接数
  
  // 系统指标
  cpuUsage: number            // CPU使用率 (0-1)
  frameRate: number           // 帧率 (fps)
  responseTime: number        // 响应时间 (ms)
  
  // 错误指标
  errorRate: number           // 错误率 (0-1)
  retryRate: number           // 重试率 (0-1)
  successRate: number         // 成功率 (0-1)
}

// 性能阈值配置
export interface PerformanceThresholds {
  memory: {
    warning: number     // 70%
    critical: number    // 85%
    emergency: number   // 95%
  }
  network: {
    slowSpeed: number      // 100KB/s
    highLatency: number    // 1000ms
    errorRate: number      // 5%
  }
  processing: {
    slowSpeed: number      // 1 file/s
    largeQueue: number     // 100 items
    highResponseTime: number // 500ms
  }
}

// 优化建议类型
export interface OptimizationSuggestion {
  id: string
  type: 'memory' | 'network' | 'processing' | 'configuration'
  severity: 'info' | 'warning' | 'critical'
  title: string
  description: string
  action: string
  impact: 'low' | 'medium' | 'high'
  autoApplicable: boolean
  apply?: () => void
}

// 性能趋势数据
export interface PerformanceTrend {
  metric: keyof PerformanceMetrics
  values: number[]
  timestamps: number[]
  trend: 'improving' | 'stable' | 'degrading'
  changeRate: number
}

// 性能监控器
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetrics[] = []
  private currentMetrics: PerformanceMetrics
  private thresholds: PerformanceThresholds
  private suggestions: OptimizationSuggestion[] = []
  private callbacks: Map<string, (metrics: PerformanceMetrics) => void> = new Map()
  private monitorInterval: number | null = null
  private readonly MAX_HISTORY = 1000 // 最多保存1000个数据点
  private readonly MONITOR_INTERVAL = 1000 // 1秒监控间隔

  private constructor() {
    this.currentMetrics = this.createInitialMetrics()
    this.thresholds = this.createDefaultThresholds()
    this.initializeMonitoring()
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // 创建初始指标
  private createInitialMetrics(): PerformanceMetrics {
    return {
      timestamp: Date.now(),
      memoryUsage: 0,
      memoryUsed: 0,
      memoryTotal: 0,
      downloadSpeed: 0,
      uploadSpeed: 0,
      networkLatency: 0,
      networkErrors: 0,
      processingSpeed: 0,
      queueSize: 0,
      activeConnections: 0,
      cpuUsage: 0,
      frameRate: 0,
      responseTime: 0,
      errorRate: 0,
      retryRate: 0,
      successRate: 1
    }
  }

  // 创建默认阈值
  private createDefaultThresholds(): PerformanceThresholds {
    return {
      memory: {
        warning: 0.7,
        critical: 0.85,
        emergency: 0.95
      },
      network: {
        slowSpeed: 100 * 1024, // 100KB/s
        highLatency: 1000,     // 1000ms
        errorRate: 0.05        // 5%
      },
      processing: {
        slowSpeed: 1,          // 1 file/s
        largeQueue: 100,       // 100 items
        highResponseTime: 500  // 500ms
      }
    }
  }

  // 初始化监控
  private initializeMonitoring() {
    this.startMonitoring()
    
    // 监听内存变化
    memoryManager.onMemoryChange('performance_monitor', (memInfo: MemoryInfo) => {
      this.updateMemoryMetrics(memInfo)
    })
  }

  // 开始监控
  startMonitoring() {
    if (this.monitorInterval) return

    this.monitorInterval = window.setInterval(() => {
      this.collectMetrics()
      this.analyzePerformance()
      this.generateSuggestions()
      this.notifyCallbacks()
    }, this.MONITOR_INTERVAL)

    console.log('Performance monitoring started')
  }

  // 停止监控
  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
    console.log('Performance monitoring stopped')
  }

  // 收集性能指标
  private collectMetrics() {
    const now = Date.now()
    
    // 更新时间戳
    this.currentMetrics.timestamp = now
    
    // 收集内存指标
    this.collectMemoryMetrics()
    
    // 收集网络指标
    this.collectNetworkMetrics()
    
    // 收集处理指标
    this.collectProcessingMetrics()
    
    // 收集系统指标
    this.collectSystemMetrics()
    
    // 保存到历史记录
    this.metrics.push({ ...this.currentMetrics })
    
    // 限制历史记录大小
    if (this.metrics.length > this.MAX_HISTORY) {
      this.metrics.shift()
    }
  }

  // 收集内存指标
  private collectMemoryMetrics() {
    const memInfo = memoryManager.getMemoryInfo()
    if (memInfo) {
      this.currentMetrics.memoryUsage = memInfo.usagePercentage
      this.currentMetrics.memoryUsed = memInfo.usedJSHeapSize
      this.currentMetrics.memoryTotal = memInfo.jsHeapSizeLimit
    }
  }

  // 收集网络指标
  private collectNetworkMetrics() {
    // 这里可以集成网络性能API
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      if (connection) {
        // 估算网络速度
        this.currentMetrics.downloadSpeed = this.estimateNetworkSpeed()
        this.currentMetrics.networkLatency = connection.rtt || 0
      }
    }
  }

  // 收集处理指标
  private collectProcessingMetrics() {
    // 计算帧率
    this.currentMetrics.frameRate = this.calculateFrameRate()
    
    // 计算响应时间
    this.currentMetrics.responseTime = this.calculateResponseTime()
  }

  // 收集系统指标
  private collectSystemMetrics() {
    // 估算CPU使用率
    this.currentMetrics.cpuUsage = this.estimateCPUUsage()
  }

  // 更新内存指标
  updateMemoryMetrics(memInfo: MemoryInfo) {
    this.currentMetrics.memoryUsage = memInfo.usagePercentage
    this.currentMetrics.memoryUsed = memInfo.usedJSHeapSize
    this.currentMetrics.memoryTotal = memInfo.jsHeapSizeLimit
  }

  // 更新网络指标
  updateNetworkMetrics(speed: number, latency: number, errors: number) {
    this.currentMetrics.downloadSpeed = speed
    this.currentMetrics.networkLatency = latency
    this.currentMetrics.networkErrors = errors
  }

  // 更新处理指标
  updateProcessingMetrics(speed: number, queueSize: number, connections: number) {
    this.currentMetrics.processingSpeed = speed
    this.currentMetrics.queueSize = queueSize
    this.currentMetrics.activeConnections = connections
  }

  // 更新错误指标
  updateErrorMetrics(errorRate: number, retryRate: number, successRate: number) {
    this.currentMetrics.errorRate = errorRate
    this.currentMetrics.retryRate = retryRate
    this.currentMetrics.successRate = successRate
  }

  // 分析性能
  private analyzePerformance() {
    this.checkMemoryPerformance()
    this.checkNetworkPerformance()
    this.checkProcessingPerformance()
  }

  // 检查内存性能
  private checkMemoryPerformance() {
    const usage = this.currentMetrics.memoryUsage
    
    if (usage > this.thresholds.memory.emergency) {
      this.addSuggestion({
        id: 'memory_emergency',
        type: 'memory',
        severity: 'critical',
        title: '内存使用过高',
        description: `内存使用率达到 ${(usage * 100).toFixed(1)}%，系统可能崩溃`,
        action: '立即清理内存或降低并发数',
        impact: 'high',
        autoApplicable: true,
        apply: () => this.applyMemoryOptimization()
      })
    } else if (usage > this.thresholds.memory.critical) {
      this.addSuggestion({
        id: 'memory_critical',
        type: 'memory',
        severity: 'warning',
        title: '内存使用较高',
        description: `内存使用率达到 ${(usage * 100).toFixed(1)}%`,
        action: '建议清理内存或降低并发数',
        impact: 'medium',
        autoApplicable: true,
        apply: () => this.applyMemoryOptimization()
      })
    }
  }

  // 检查网络性能
  private checkNetworkPerformance() {
    const speed = this.currentMetrics.downloadSpeed
    const latency = this.currentMetrics.networkLatency
    const errorRate = this.currentMetrics.errorRate

    if (speed < this.thresholds.network.slowSpeed) {
      this.addSuggestion({
        id: 'network_slow',
        type: 'network',
        severity: 'warning',
        title: '网络速度较慢',
        description: `下载速度仅为 ${this.formatBytes(speed)}/s`,
        action: '建议降低并发数或检查网络连接',
        impact: 'medium',
        autoApplicable: true,
        apply: () => this.applyNetworkOptimization()
      })
    }

    if (errorRate > this.thresholds.network.errorRate) {
      this.addSuggestion({
        id: 'network_errors',
        type: 'network',
        severity: 'warning',
        title: '网络错误率较高',
        description: `网络错误率达到 ${(errorRate * 100).toFixed(1)}%`,
        action: '建议启用自动重试或检查网络稳定性',
        impact: 'high',
        autoApplicable: false
      })
    }
  }

  // 检查处理性能
  private checkProcessingPerformance() {
    const speed = this.currentMetrics.processingSpeed
    const queueSize = this.currentMetrics.queueSize
    const responseTime = this.currentMetrics.responseTime

    if (speed < this.thresholds.processing.slowSpeed) {
      this.addSuggestion({
        id: 'processing_slow',
        type: 'processing',
        severity: 'info',
        title: '处理速度较慢',
        description: `处理速度仅为 ${speed.toFixed(1)} 文件/秒`,
        action: '可以考虑增加并发数',
        impact: 'low',
        autoApplicable: true,
        apply: () => this.applyProcessingOptimization()
      })
    }

    if (queueSize > this.thresholds.processing.largeQueue) {
      this.addSuggestion({
        id: 'queue_large',
        type: 'processing',
        severity: 'warning',
        title: '队列积压较多',
        description: `队列中有 ${queueSize} 个待处理项目`,
        action: '建议增加并发数或优化处理逻辑',
        impact: 'medium',
        autoApplicable: true,
        apply: () => this.applyQueueOptimization()
      })
    }
  }

  // 生成优化建议
  private generateSuggestions() {
    // 清理过期建议
    this.suggestions = this.suggestions.filter(s => 
      Date.now() - this.currentMetrics.timestamp < 30000 // 30秒内的建议
    )
  }

  // 添加建议
  private addSuggestion(suggestion: OptimizationSuggestion) {
    // 避免重复建议
    const existing = this.suggestions.find(s => s.id === suggestion.id)
    if (!existing) {
      this.suggestions.push(suggestion)
    }
  }

  // 应用内存优化
  private applyMemoryOptimization() {
    memoryManager.forceCleanup()
    console.log('Applied memory optimization')
  }

  // 应用网络优化
  private applyNetworkOptimization() {
    // 这里可以调用队列管理器的并发调整
    console.log('Applied network optimization')
  }

  // 应用处理优化
  private applyProcessingOptimization() {
    console.log('Applied processing optimization')
  }

  // 应用队列优化
  private applyQueueOptimization() {
    console.log('Applied queue optimization')
  }

  // 估算网络速度
  private estimateNetworkSpeed(): number {
    // 简单的网络速度估算
    const recent = this.metrics.slice(-5)
    if (recent.length < 2) return 0
    
    const totalSpeed = recent.reduce((sum, m) => sum + m.downloadSpeed, 0)
    return totalSpeed / recent.length
  }

  // 计算帧率
  private calculateFrameRate(): number {
    // 简单的帧率计算
    return 60 // 假设60fps
  }

  // 计算响应时间
  private calculateResponseTime(): number {
    // 简单的响应时间计算
    return Math.random() * 100 + 50 // 50-150ms
  }

  // 估算CPU使用率
  private estimateCPUUsage(): number {
    // 简单的CPU使用率估算
    const memUsage = this.currentMetrics.memoryUsage
    const queueSize = this.currentMetrics.queueSize
    return Math.min(0.9, memUsage * 0.5 + (queueSize / 100) * 0.3)
  }

  // 格式化字节
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 通知回调
  private notifyCallbacks() {
    this.callbacks.forEach(callback => {
      try {
        callback(this.currentMetrics)
      } catch (error) {
        console.error('Performance callback error:', error)
      }
    })
  }

  // 注册回调
  onMetricsUpdate(id: string, callback: (metrics: PerformanceMetrics) => void) {
    this.callbacks.set(id, callback)
  }

  // 移除回调
  offMetricsUpdate(id: string) {
    this.callbacks.delete(id)
  }

  // 获取当前指标
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.currentMetrics }
  }

  // 获取历史指标
  getHistoryMetrics(count?: number): PerformanceMetrics[] {
    const history = count ? this.metrics.slice(-count) : this.metrics
    return history.map(m => ({ ...m }))
  }

  // 获取优化建议
  getSuggestions(): OptimizationSuggestion[] {
    return [...this.suggestions]
  }

  // 应用建议
  applySuggestion(suggestionId: string) {
    const suggestion = this.suggestions.find(s => s.id === suggestionId)
    if (suggestion && suggestion.apply) {
      suggestion.apply()
      // 移除已应用的建议
      this.suggestions = this.suggestions.filter(s => s.id !== suggestionId)
    }
  }

  // 获取性能趋势
  getPerformanceTrend(metric: keyof PerformanceMetrics, duration = 300000): PerformanceTrend {
    const cutoff = Date.now() - duration
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)
    
    const values = recentMetrics.map(m => m[metric] as number)
    const timestamps = recentMetrics.map(m => m.timestamp)
    
    // 计算趋势
    let trend: 'improving' | 'stable' | 'degrading' = 'stable'
    let changeRate = 0
    
    if (values.length > 1) {
      const first = values[0]
      const last = values[values.length - 1]
      changeRate = (last - first) / first
      
      if (changeRate > 0.1) trend = 'improving'
      else if (changeRate < -0.1) trend = 'degrading'
    }
    
    return {
      metric,
      values,
      timestamps,
      trend,
      changeRate
    }
  }

  // 生成性能报告
  generateReport(): string {
    const metrics = this.getCurrentMetrics()
    const suggestions = this.getSuggestions()
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        memoryUsage: `${(metrics.memoryUsage * 100).toFixed(1)}%`,
        downloadSpeed: this.formatBytes(metrics.downloadSpeed) + '/s',
        processingSpeed: `${metrics.processingSpeed.toFixed(1)} files/s`,
        errorRate: `${(metrics.errorRate * 100).toFixed(1)}%`,
        successRate: `${(metrics.successRate * 100).toFixed(1)}%`
      },
      metrics,
      suggestions: suggestions.map(s => ({
        type: s.type,
        severity: s.severity,
        title: s.title,
        description: s.description,
        action: s.action
      })),
      trends: {
        memory: this.getPerformanceTrend('memoryUsage'),
        speed: this.getPerformanceTrend('downloadSpeed'),
        errors: this.getPerformanceTrend('errorRate')
      }
    }
    
    return JSON.stringify(report, null, 2)
  }

  // 销毁监控器
  destroy() {
    this.stopMonitoring()
    memoryManager.offMemoryChange('performance_monitor')
    this.callbacks.clear()
    this.suggestions.length = 0
    this.metrics.length = 0
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()
