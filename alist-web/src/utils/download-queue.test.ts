// 简单的测试文件，用于验证下载队列功能
import { DownloadQueueManager, DOWNLOAD_CONFIG, PerformanceMonitor } from './download-queue'
import { memoryManager } from './memory-manager'
import { ErrorType, EnhancedError } from './error-handler'

// 模拟数据
const mockObj = {
  name: 'test-file.txt',
  size: 1024,
  is_dir: false,
  modified: new Date(),
  sign: '',
  thumb: '',
  type: 0
}

const mockFolder = {
  name: 'test-folder',
  size: 0,
  is_dir: true,
  modified: new Date(),
  sign: '',
  thumb: '',
  type: 1
}

// 模拟API函数
const mockFsList = async (path: string, password?: string) => {
  console.log(`Mock fsList called with path: ${path}`)
  return {
    code: 200,
    data: {
      content: [mockObj, mockObj, mockObj] // 模拟3个文件
    }
  }
}

const mockGetLinkByDirAndObj = (dir: string, obj: any, type?: string, encodeAll?: boolean) => {
  return `http://localhost:5244/d${dir}/${obj.name}`
}

// 测试函数
export const testDownloadQueue = async () => {
  console.log('=== 测试下载队列管理器 ===')
  
  const manager = new DownloadQueueManager()
  const monitor = new PerformanceMonitor()
  
  // 设置进度回调
  manager.setProgressCallback((progress) => {
    console.log(`进度更新: ${progress.phase} - ${progress.scannedFiles}/${progress.totalFiles}`)
  })
  
  try {
    // 测试扫描文件结构
    console.log('开始扫描文件结构...')
    const files = await manager.scanFolderStructure(
      [mockFolder],
      mockFsList,
      mockGetLinkByDirAndObj,
      '/test',
      ''
    )
    
    console.log(`扫描完成，找到 ${files.length} 个文件`)
    files.forEach(file => {
      console.log(`- ${file.path} (${file.size} bytes)`)
    })
    
    // 测试性能监控
    console.log('测试性能监控...')
    monitor.checkPerformance(manager)
    
    // 测试并发调整
    console.log('测试并发调整...')
    manager.adjustConcurrency('reduce')
    manager.adjustConcurrency('increase')
    
    console.log('测试完成！')
    
  } catch (error) {
    console.error('测试失败:', error)
  } finally {
    manager.destroy()
  }
}

// 配置验证
export const validateConfig = () => {
  console.log('=== 验证配置 ===')
  console.log('BATCH_SIZE:', DOWNLOAD_CONFIG.BATCH_SIZE)
  console.log('CONCURRENT_API_CALLS:', DOWNLOAD_CONFIG.CONCURRENT_API_CALLS)
  console.log('CONCURRENT_DOWNLOADS:', DOWNLOAD_CONFIG.CONCURRENT_DOWNLOADS)
  console.log('API_TIMEOUT:', DOWNLOAD_CONFIG.API_TIMEOUT)
  console.log('DOWNLOAD_TIMEOUT:', DOWNLOAD_CONFIG.DOWNLOAD_TIMEOUT)
  console.log('MAX_RETRIES:', DOWNLOAD_CONFIG.MAX_RETRIES)
  console.log('RETRY_DELAY:', DOWNLOAD_CONFIG.RETRY_DELAY)
}

// 内存使用测试
export const testMemoryUsage = () => {
  console.log('=== 内存使用测试 ===')
  
  if ((performance as any).memory) {
    const memory = (performance as any).memory
    console.log('当前内存使用:')
    console.log('- 已使用:', Math.round(memory.usedJSHeapSize / 1024 / 1024), 'MB')
    console.log('- 总分配:', Math.round(memory.totalJSHeapSize / 1024 / 1024), 'MB')
    console.log('- 限制:', Math.round(memory.jsHeapSizeLimit / 1024 / 1024), 'MB')
    console.log('- 使用率:', Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100), '%')
  } else {
    console.log('浏览器不支持内存监控')
  }
}

// 测试内存管理
export const testMemoryManager = async () => {
  console.log('=== 测试内存管理器 ===')

  // 测试内存监控
  const memInfo = memoryManager.getMemoryInfo()
  if (memInfo) {
    console.log('当前内存信息:')
    console.log('- 使用率:', (memInfo.usagePercentage * 100).toFixed(2) + '%')
    console.log('- 已使用:', memoryManager.formatMemorySize(memInfo.usedJSHeapSize))
    console.log('- 总限制:', memoryManager.formatMemorySize(memInfo.jsHeapSizeLimit))
  }

  // 测试内存建议
  if (memInfo) {
    const advice = memoryManager.getMemoryAdvice(memInfo)
    if (advice.length > 0) {
      console.log('内存建议:')
      advice.forEach(tip => console.log('- ' + tip))
    }
  }

  // 测试内存清理
  console.log('执行内存清理...')
  memoryManager.performCleanup()

  console.log('内存管理器测试完成')
}

// 测试错误处理
export const testErrorHandling = async () => {
  console.log('=== 测试错误处理 ===')

  const manager = new DownloadQueueManager()

  try {
    // 模拟网络错误
    const networkError = new Error('fetch failed')
    networkError.name = 'TypeError'
    throw networkError
  } catch (error) {
    console.log('捕获到错误，进行分类...')
    const errorStats = manager.getErrorStats()
    console.log('错误统计:', errorStats)

    const advice = manager.getErrorAdvice()
    if (advice.length > 0) {
      console.log('错误建议:')
      advice.forEach(tip => console.log('- ' + tip))
    }
  }

  manager.destroy()
  console.log('错误处理测试完成')
}

// 压力测试
export const stressTest = async () => {
  console.log('=== 压力测试 ===')

  const manager = new DownloadQueueManager()
  const monitor = new PerformanceMonitor()

  // 模拟大量文件
  const largeFileList = Array.from({ length: 1000 }, (_, i) => ({
    name: `file_${i}.txt`,
    size: Math.random() * 1024 * 1024, // 随机大小
    is_dir: false,
    modified: new Date(),
    sign: '',
    thumb: '',
    type: 0
  }))

  console.log(`开始处理 ${largeFileList.length} 个文件...`)

  const startTime = Date.now()
  let processedCount = 0

  // 设置进度回调
  manager.setProgressCallback((progress) => {
    processedCount = progress.scannedFiles
    if (processedCount % 100 === 0) {
      console.log(`已处理: ${processedCount}/${largeFileList.length}`)

      // 检查性能
      monitor.checkPerformance(manager)

      // 检查内存
      const memInfo = manager.getMemoryInfo()
      if (memInfo && memInfo.usagePercentage > 0.8) {
        console.warn('内存使用过高，建议停止测试')
      }
    }
  })

  try {
    // 这里应该调用实际的扫描方法，但为了测试我们只是模拟
    console.log('模拟扫描过程...')
    await new Promise(resolve => setTimeout(resolve, 5000))

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log(`压力测试完成:`)
    console.log(`- 处理时间: ${duration}ms`)
    console.log(`- 处理速度: ${(largeFileList.length / (duration / 1000)).toFixed(2)} files/sec`)

    // 获取性能报告
    const report = monitor.getPerformanceReport()
    console.log('性能报告:', report)

  } catch (error) {
    console.error('压力测试失败:', error)
  } finally {
    manager.destroy()
  }
}

// 在开发环境中可以调用这些测试函数
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // 将测试函数暴露到全局，方便在控制台调用
  (window as any).testDownloadQueue = testDownloadQueue
  (window as any).validateConfig = validateConfig
  (window as any).testMemoryUsage = testMemoryUsage
  (window as any).testMemoryManager = testMemoryManager
  (window as any).testErrorHandling = testErrorHandling
  (window as any).stressTest = stressTest

  console.log('下载队列测试函数已加载，可在控制台调用:')
  console.log('- testDownloadQueue() - 测试下载队列')
  console.log('- validateConfig() - 验证配置')
  console.log('- testMemoryUsage() - 测试内存使用')
  console.log('- testMemoryManager() - 测试内存管理器')
  console.log('- testErrorHandling() - 测试错误处理')
  console.log('- stressTest() - 压力测试')
}
