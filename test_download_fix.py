#!/usr/bin/env python3
"""
测试文件夹打包下载修复
验证前端不再出现 TypeError: null is not an object 错误
验证p-queue模块导入问题已修复
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:5244"
USERNAME = "8844"
PASSWORD = "8844"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 200:
            return data["data"]["token"]
    
    print("❌ 登录失败")
    return None

def test_capacity_data_check(token):
    """测试容量数据检查API"""
    headers = {"Authorization": token}
    
    print("🔍 测试容量数据检查API...")
    response = requests.get(f"{BASE_URL}/api/me/capacity/check", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ API响应成功")
        
        # 检查关键字段
        if data.get("code") == 200 and data.get("data"):
            response_data = data["data"]
            
            # 验证 paths_need_initialization 不是 null
            paths_need_init = response_data.get("paths_need_initialization")
            if paths_need_init is None:
                print("❌ paths_need_initialization 仍然是 null")
                return False
            elif isinstance(paths_need_init, list):
                print(f"✅ paths_need_initialization 是数组: {len(paths_need_init)} 个路径")
            else:
                print(f"⚠️  paths_need_initialization 类型异常: {type(paths_need_init)}")
                return False
            
            # 验证其他字段
            paths_with_data = response_data.get("paths_with_existing_data")
            if isinstance(paths_with_data, list):
                print(f"✅ paths_with_existing_data 是数组: {len(paths_with_data)} 个路径")
            
            total_paths = response_data.get("total_capacity_paths")
            if isinstance(total_paths, int):
                print(f"✅ total_capacity_paths 是数字: {total_paths}")
            
            return True
        else:
            print(f"❌ API返回错误: {data}")
            return False
    else:
        print(f"❌ API请求失败: {response.status_code}")
        return False

def test_file_list_api(token):
    """测试文件列表API"""
    headers = {"Authorization": token}
    
    print("\n🔍 测试文件列表API...")
    
    # 测试虚拟存储空间
    response = requests.post(f"{BASE_URL}/api/fs/list", 
                           json={"path": "/virtual/storage/1"}, 
                           headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 200:
            print(f"✅ 虚拟存储空间文件列表获取成功")
            content = data.get("data", {}).get("content", [])
            print(f"📁 找到 {len(content)} 个文件/文件夹")
            return True
        else:
            print(f"❌ 文件列表API返回错误: {data}")
            return False
    else:
        print(f"❌ 文件列表API请求失败: {response.status_code}")
        return False

def main():
    print("🧪 开始测试文件夹打包下载修复...")
    
    # 登录
    token = login()
    if not token:
        sys.exit(1)
    
    print(f"✅ 登录成功")
    
    # 测试容量数据检查
    success1 = test_capacity_data_check(token)
    
    # 测试文件列表API
    success2 = test_file_list_api(token)
    
    # 总结
    print("\n📋 测试结果总结:")
    print(f"  容量数据检查API: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"  文件列表API: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 后端API测试通过！")
        print("💡 现在可以测试前端页面:")
        print(f"   1. 访问 http://localhost:5173")
        print(f"   2. 登录用户: {USERNAME}")
        print(f"   3. 检查控制台是否还有 'null is not an object' 错误")
        print(f"   4. 尝试文件夹打包下载功能")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
