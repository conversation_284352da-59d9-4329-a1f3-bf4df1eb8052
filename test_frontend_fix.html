<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AList 前端修复测试</h1>
        
        <div class="info">
            <strong>测试目标：</strong>
            <ul>
                <li>验证容量数据检查不再出现 "null is not an object" 错误</li>
                <li>验证p-queue模块导入正常</li>
                <li>验证文件夹打包下载功能正常</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 容量数据检查测试</h3>
            <button onclick="testCapacityAPI()">测试容量API</button>
            <div id="capacityResult"></div>
        </div>

        <div class="test-section">
            <h3>📦 下载队列模块测试</h3>
            <button onclick="testPQueueImport()">测试p-queue导入</button>
            <div id="pqueueResult"></div>
        </div>

        <div class="test-section">
            <h3>📁 文件列表测试</h3>
            <button onclick="testFileList()">测试文件列表</button>
            <div id="fileListResult"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:5244';
        let authToken = null;

        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${success ? 'success' : 'error'}">${message}</div>`;
        }

        async function login() {
            if (authToken) return authToken;

            try {
                log('正在登录...');
                const response = await fetch(`${BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '8844',
                        password: '8844'
                    })
                });

                const data = await response.json();
                if (data.code === 200) {
                    authToken = data.data.token;
                    log('✅ 登录成功');
                    return authToken;
                } else {
                    throw new Error(data.message || '登录失败');
                }
            } catch (error) {
                log(`❌ 登录失败: ${error.message}`);
                throw error;
            }
        }

        async function testCapacityAPI() {
            try {
                const token = await login();
                log('🔍 测试容量数据检查API...');

                const response = await fetch(`${BASE_URL}/api/me/capacity/check`, {
                    headers: {
                        'Authorization': token
                    }
                });

                const data = await response.json();
                log(`API响应: ${JSON.stringify(data, null, 2)}`);

                if (data.code === 200 && data.data) {
                    const { paths_need_initialization, paths_with_existing_data, total_capacity_paths } = data.data;
                    
                    // 检查关键字段类型
                    const isArrayInit = Array.isArray(paths_need_initialization);
                    const isArrayExisting = Array.isArray(paths_with_existing_data);
                    const isNumberTotal = typeof total_capacity_paths === 'number';

                    if (isArrayInit && isArrayExisting && isNumberTotal) {
                        showResult('capacityResult', true, 
                            `✅ 容量API测试通过！<br>
                            - paths_need_initialization: 数组 (${paths_need_initialization.length} 项)<br>
                            - paths_with_existing_data: 数组 (${paths_with_existing_data.length} 项)<br>
                            - total_capacity_paths: 数字 (${total_capacity_paths})`);
                        log('✅ 容量数据检查API测试通过');
                    } else {
                        throw new Error('数据类型检查失败');
                    }
                } else {
                    throw new Error(data.message || 'API返回错误');
                }
            } catch (error) {
                showResult('capacityResult', false, `❌ 测试失败: ${error.message}`);
                log(`❌ 容量API测试失败: ${error.message}`);
            }
        }

        async function testPQueueImport() {
            try {
                log('🔍 测试p-queue模块导入...');
                
                // 尝试动态导入p-queue
                const PQueue = await import('p-queue');
                log('✅ p-queue模块导入成功');
                
                // 尝试创建队列实例
                const queue = new PQueue.default({ concurrency: 2 });
                log('✅ p-queue实例创建成功');
                
                // 测试队列功能
                let testResult = '';
                await queue.add(async () => {
                    testResult = 'Queue test passed';
                });
                
                if (testResult === 'Queue test passed') {
                    showResult('pqueueResult', true, '✅ p-queue模块测试通过！模块导入和功能正常');
                    log('✅ p-queue功能测试通过');
                } else {
                    throw new Error('队列功能测试失败');
                }
            } catch (error) {
                showResult('pqueueResult', false, `❌ p-queue测试失败: ${error.message}`);
                log(`❌ p-queue测试失败: ${error.message}`);
            }
        }

        async function testFileList() {
            try {
                const token = await login();
                log('🔍 测试文件列表API...');

                const response = await fetch(`${BASE_URL}/api/fs/list`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify({
                        path: '/virtual/storage/1'
                    })
                });

                const data = await response.json();
                log(`文件列表响应: ${JSON.stringify(data, null, 2)}`);

                if (data.code === 200) {
                    const content = data.data?.content || [];
                    showResult('fileListResult', true, 
                        `✅ 文件列表获取成功！<br>
                        - 路径: /virtual/storage/1<br>
                        - 文件/文件夹数量: ${content.length}`);
                    log(`✅ 文件列表测试通过，找到 ${content.length} 个项目`);
                } else {
                    throw new Error(data.message || 'API返回错误');
                }
            } catch (error) {
                showResult('fileListResult', false, `❌ 测试失败: ${error.message}`);
                log(`❌ 文件列表测试失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testCapacityAPI();
            }, 1000);
        });
    </script>
</body>
</html>
